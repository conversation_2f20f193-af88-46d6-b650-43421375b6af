{"name": "Enforce-Guardrails-Storage", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Storage Account", "description": "This policy initiative is a group of policies that ensures Storage is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "Storage", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"storageKeysExpiration": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountNetworkRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountRestrictNetworkRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageThreatProtection": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "storageClassicToArm": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsInfraEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountSharedKey": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsCrossTenant": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsDoubleEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsCopyScope": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsAllowedCopyScope": {"type": "string", "defaultValue": "AAD"}, "storageServicesEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageLocalUser": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageSftp": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageNetworkAclsBypass": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAllowedNetworkAclsBypass": {"type": "array", "defaultValue": ["None"]}, "storageResourceAccessRulesTenantId": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageResourceAccessRulesResourceId": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageNetworkAclsVirtualNetworkRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageContainerDeleteRetentionPolicy": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageMinContainerDeleteRetentionInDays": {"type": "Integer", "defaultValue": 7}, "storageCorsRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "modifyStorageFileSyncPublicEndpoint": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "modifyStorageAccountPublicEndpoint": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "storageAccountsModifyDisablePublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-Storage-CopyScope", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-CopyScope", "parameters": {"effect": {"value": "[parameters('storageAccountsCopyScope')]"}, "allowedCopyScope": {"value": "[parameters('storageAccountsAllowedCopyScope')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-ServicesEncryption", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-ServicesEncryption", "parameters": {"effect": {"value": "[parameters('storageServicesEncryption')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-LocalUser", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-LocalUser", "parameters": {"effect": {"value": "[parameters('storageLocalUser')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-SFTP", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-SFTP", "parameters": {"effect": {"value": "[parameters('storageSftp')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-NetworkAclsBypass", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-NetworkAclsBypass", "parameters": {"effect": {"value": "[parameters('storageNetworkAclsBypass')]"}, "allowedBypassOptions": {"value": "[parameters('storageAllowedNetworkAclsBypass')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-ResourceAccessRulesTenantId", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-ResourceAccessRulesTenantId", "parameters": {"effect": {"value": "[parameters('storageResourceAccessRulesTenantId')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-ResourceAccessRulesResourceId", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-ResourceAccessRulesResourceId", "parameters": {"effect": {"value": "[parameters('storageResourceAccessRulesResourceId')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-NetworkAclsVirtualNetworkRules", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-NetworkAclsVirtualNetworkRules", "parameters": {"effect": {"value": "[parameters('storageNetworkAclsVirtualNetworkRules')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-ContainerDeleteRetentionPolicy", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-ContainerDeleteRetentionPolicy", "parameters": {"effect": {"value": "[parameters('storageContainerDeleteRetentionPolicy')]"}, "minContainerDeleteRetentionInDays": {"value": "[parameters('storageMinContainerDeleteRetentionInDays')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-CorsRules", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-CorsRules", "parameters": {"effect": {"value": "[parameters('storageCorsRules')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-Account-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bfecdea6-31c4-4045-ad42-71b9dc87247d", "parameters": {"effect": {"value": "[parameters('storageAccountsDoubleEncryption')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-Cross-Tenant", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/92a89a79-6c52-4a7e-a03f-61306fc49312", "parameters": {"effect": {"value": "[parameters('storageAccountsCrossTenant')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-Shared-Key", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8c6a50c6-9ffd-4ae7-986f-5fa6111f9a54", "parameters": {"effect": {"value": "[parameters('storageAccountSharedKey')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-Infra-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4733ea7b-a883-42fe-8cac-97454c2a9e4a", "parameters": {"effect": {"value": "[parameters('storageAccountsInfraEncryption')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-Classic", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/37e0d2fe-28a5-43d6-a273-67d37d1f5606", "parameters": {"effect": {"value": "[parameters('storageClassicToArm')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Dine-Storage-Threat-Protection", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/361c2074-3595-4e5d-8cab-4f21dffc835c", "parameters": {"effect": {"value": "[parameters('storageThreatProtection')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-Restrict-NetworkRules", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/34c877ad-507e-4c82-993e-3452a6e0ad3c", "parameters": {"effect": {"value": "[parameters('storageAccountRestrictNetworkRules')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-NetworkRules", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2a1a9cdf-e04d-429a-8416-3bfb72a1b26f", "parameters": {"effect": {"value": "[parameters('storageAccountNetworkRules')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Storage-Account-Keys-Expire", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/044985bb-afe1-42cd-8a36-9d5d42424537", "parameters": {"effect": {"value": "[parameters('storageKeysExpiration')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Storage-FileSync-PublicEndpoint", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0e07b2e9-6cd9-4c40-9ccb-52817b95133b", "parameters": {"effect": {"value": "[parameters('modifyStorageFileSyncPublicEndpoint')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Blob-Storage-Account-PublicEndpoint", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/********-8df0-4414-9937-de9c5c4e396b", "parameters": {"effect": {"value": "[parameters('modifyStorageAccountPublicEndpoint')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Storage-Account-PublicEndpoint", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a06d0189-92e8-4dba-b0c4-08d7669fce7d", "parameters": {"effect": {"value": "[parameters('storageAccountsModifyDisablePublicNetworkAccess')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}