{"name": "Deny-Storage-minTLS", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "[Deprecated] Storage Account set to minimum TLS and Secure transfer should be enabled", "description": "Audit requirement of Secure transfer in your storage account. This policy is superseded by https://www.azadvertizer.net/azpolicyadvertizer/fe83a0eb-a853-422d-aac2-1bffd182c5d0.html and https://www.azadvertizer.net/azpolicyadvertizer/404c3081-a854-4457-ae30-26a93ef643f9.html", "metadata": {"deprecated": true, "supersededBy": "fe83a0eb-a853-422d-aac2-1bffd182c5d0,404c3081-a854-4457-ae30-26a93ef643f9", "version": "1.0.0-deprecated", "category": "Storage", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Effect", "description": "The effect determines what happens when the policy rule is evaluated to match"}}, "minimumTlsVersion": {"type": "String", "defaultValue": "TLS1_2", "allowedValues": ["TLS1_2", "TLS1_1", "TLS1_0"], "metadata": {"displayName": "Storage Account select minimum TLS version", "description": "Select version  minimum TLS version on Azure Storage Account to enforce"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"anyOf": [{"allOf": [{"value": "[requestContext().apiVersion]", "less": "2019-04-01"}, {"field": "Microsoft.Storage/storageAccounts/supportsHttpsTrafficOnly", "exists": "false"}]}, {"field": "Microsoft.Storage/storageAccounts/supportsHttpsTrafficOnly", "equals": "false"}, {"field": "Microsoft.Storage/storageAccounts/minimumTlsVersion", "notequals": "[parameters('minimumTlsVersion')]"}, {"field": "Microsoft.Storage/storageAccounts/minimumTlsVersion", "exists": "false"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}