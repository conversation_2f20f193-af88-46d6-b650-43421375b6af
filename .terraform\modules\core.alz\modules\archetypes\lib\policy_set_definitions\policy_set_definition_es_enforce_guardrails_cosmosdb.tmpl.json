{"name": "Enforce-Guardrails-CosmosDb", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Cosmos DB", "description": "This policy initiative is a group of policies that ensures Cosmos DB is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "Cosmos DB", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"cosmosDbLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cosmosDbFwRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cosmosDbAtp": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "cosmosDbModifyLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "cosmosDbModifyPublicAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Modify-CosmosDb-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/dc2d41d1-4ab1-4666-a3e1-3d51c43e0049", "parameters": {"effect": {"value": "[parameters('cosmosDbModifyLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Dine-CosmosDb-Atp", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b5f04e03-92a3-4b09-9410-2cc5e5047656", "parameters": {"effect": {"value": "[parameters('cosmosDbAtp')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-CosmosDb-Fw-Rules", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/862e97cf-49fc-4a5c-9de4-40d4e2e7c8eb", "parameters": {"effect": {"value": "[parameters('cosmosDbFwRules')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-CosmosDb-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5450f5bd-9c72-4390-a9c4-a7aba4edfdd2", "parameters": {"effect": {"value": "[parameters('cosmosDbLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Append-CosmosDb-Metadata", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4750c32b-89c0-46af-bfcb-2e4541a818d5", "parameters": {}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-CosmosDb-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/da69ba51-aaf1-41e5-8651-607cd0b37088", "parameters": {"effect": {"value": "[parameters('cosmosDbModifyPublicAccess')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}