{"name": "Enforce-Guardrails-DataExplorer", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Data Explorer", "description": "This policy initiative is a group of policies that ensures Data Explorer is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "Azure Data Explorer", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"adxEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adxDoubleEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adxSku": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adxModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-ADX-Sku-without-PL-Support", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1fec9658-933f-4b3e-bc95-913ed22d012b", "parameters": {"effect": {"value": "[parameters('adxSku')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-ADX-Double-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ec068d99-e9c7-401f-8cef-5bdde4e6ccf1", "parameters": {"effect": {"value": "[parameters('adxDoubleEncryption')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-ADX-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f4b53539-8df9-40e4-86c6-6b607703bd4e", "parameters": {"effect": {"value": "[parameters('adxEncryption')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-ADX-Public-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7b32f193-cb28-4e15-9a98-b9556db0bafa", "parameters": {"effect": {"value": "[parameters('adxModifyPublicNetworkAccess')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}