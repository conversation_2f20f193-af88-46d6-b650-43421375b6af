{"name": "Enforce-Guardrails-OpenAI", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Open AI (Cognitive Service)", "description": "This policy initiative is a group of policies that ensures Open AI (Cognitive Service) is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Cognitive Services", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"cognitiveServicesOutboundNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesNetworkAcls": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesModifyDisableLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "cognitiveServicesDisableLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesCustomerStorage": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesManagedIdentity": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "azureAiNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "azureAiPrivateLink": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "azureAiDisableLocalKey": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "azureAiDisableLocalKey2": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "azureAiDiagSettings": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-OpenAi-OutboundNetworkAccess", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-CognitiveServices-RestrictOutboundNetworkAccess", "parameters": {"effect": {"value": "[parameters('cognitiveServicesOutboundNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-OpenAi-NetworkAcls", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-CognitiveServices-NetworkAcls", "parameters": {"effect": {"value": "[parameters('cognitiveServicesNetworkAcls')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Cognitive-Services-Managed-Identity", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fe3fd216-4f83-4fc1-8984-2bbec80a3418", "parameters": {"effect": {"value": "[parameters('cognitiveServicesManagedIdentity')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Cognitive-Services-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/71ef260a-8f18-47b7-abcb-62d0673d94dc", "parameters": {"effect": {"value": "[parameters('cognitiveServicesDisableLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Cognitive-Services-Cust-Storage", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/46aa9b05-0e60-4eae-a88b-1e9d374fa515", "parameters": {"effect": {"value": "[parameters('cognitiveServicesCustomerStorage')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-Cognitive-Services-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/14de9e63-1b31-492e-a5a3-c3f7fd57f555", "parameters": {"effect": {"value": "[parameters('cognitiveServicesModifyDisableLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-AzureAI-Network-Access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/037eea7a-bd0a-46c5-9a66-03aea78705d3", "parameters": {"effect": {"value": "[parameters('azureAiNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Audit-AzureAI-Private-Link", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d6759c02-b87f-42b7-892e-71b3f471d782", "parameters": {"effect": {"value": "[parameters('azureAiPrivateLink')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Dine-AzureAI-Local-Key", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d45520cb-31ca-44ba-8da2-fcf914608544", "parameters": {"effect": {"value": "[parameters('azureAiDisableLocalKey')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Dine-AzureAI-Local-Key2", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/55eff01b-f2bd-4c32-9203-db285f709d30", "parameters": {"effect": {"value": "[parameters('azureAiDisableLocalKey2')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Aine-AzureAI-Diag-Settings", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b4d1c4e-934c-4703-944c-27c82c06bebb", "parameters": {"effect": {"value": "[parameters('azureAiDiagSettings')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}