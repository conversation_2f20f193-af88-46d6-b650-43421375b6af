{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deny-Privileged-AKS", "dependsOn": [], "properties": {"description": "Do not allow privileged containers creation in a Kubernetes cluster. This recommendation is part of CIS 5.2.1 which is intended to improve the security of your Kubernetes environments. This policy is generally available for Kubernetes Service (AKS), and preview for AKS Engine and Azure Arc enabled Kubernetes. For more information, see https://aka.ms/kubepolicydoc.", "displayName": "Kubernetes cluster should not allow privileged containers", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/95edb821-ddaf-4404-9732-666045e056b4", "enforcementMode": "<PERSON><PERSON><PERSON>", "parameters": {"effect": {"value": "deny"}}, "scope": "${current_scope_resource_id}", "notScopes": []}, "location": "${default_location}", "identity": {"type": "None"}}