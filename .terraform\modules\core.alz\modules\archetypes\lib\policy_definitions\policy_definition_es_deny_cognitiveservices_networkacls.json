{"name": "Deny-CognitiveServices-NetworkAcls", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Network ACLs should be restricted for Cognitive Services", "description": "Azure Cognitive Services should not allow adding individual IPs or virtual network rules to the service-level firewall. Enable this to restrict inbound network access and enforce the usage of private endpoints.", "metadata": {"version": "1.0.0", "category": "Cognitive Services", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.CognitiveServices/accounts"}, {"anyOf": [{"count": {"field": "Microsoft.CognitiveServices/accounts/networkAcls.ipRules[*]"}, "greater": 0}, {"count": {"field": "Microsoft.CognitiveServices/accounts/networkAcls.virtualNetworkRules[*]"}, "greater": 0}]}]}, "then": {"effect": "[parameters('effect')]"}}}}