{"name": "Enforce-Guardrails-KeyVault", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Azure Key Vault", "description": "Enforce recommended guardrails for Azure Key Vault.", "metadata": {"version": "2.1.0", "category": "<PERSON>", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effectKvSoftDelete": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "effectKvPurgeProtection": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "effectKvSecretsExpire": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}, "effectKvKeysExpire": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}, "effectKvFirewallEnabled": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}, "effectKvCertLifetime": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"], "defaultValue": "Audit"}, "maximumCertLifePercentageLife": {"type": "Integer", "metadata": {"displayName": "The maximum lifetime percentage", "description": "Enter the percentage of lifetime of the certificate when you want to trigger the policy action. For example, to trigger a policy action at 80% of the certificate's valid life, enter '80'."}, "defaultValue": 80}, "minimumCertLifeDaysBeforeExpiry": {"type": "Integer", "metadata": {"displayName": "The minimum days before expiry", "description": "Enter the days before expiration of the certificate when you want to trigger the policy action. For example, to trigger a policy action 90 days before the certificate's expiration, enter '90'."}, "defaultValue": 90}, "effectKvKeysLifetime": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}, "minimumKeysLifeDaysBeforeExpiry": {"type": "Integer", "metadata": {"displayName": "The minimum days before expiry", "description": "Enter the days before expiration of the certificate when you want to trigger the policy action. For example, to trigger a policy action 90 days before the certificate's expiration, enter '90'."}, "defaultValue": 90}, "effectKvSecretsLifetime": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}, "minimumSecretsLifeDaysBeforeExpiry": {"type": "Integer", "metadata": {"displayName": "The minimum days before expiry", "description": "Enter the days before expiration of the certificate when you want to trigger the policy action. For example, to trigger a policy action 90 days before the certificate's expiration, enter '90'."}, "defaultValue": 90}, "keyVaultCheckMinimumRSACertificateSize": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "keyVaultMinimumRSACertificateSizeValue": {"type": "integer", "defaultValue": 2048, "allowedValues": [2048, 3072, 4096]}, "keyVaultManagedHsmCheckMinimumRSAKeySize": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultManagedHsmMinimumRSAKeySizeValue": {"type": "integer", "defaultValue": 2048, "allowedValues": [2048, 3072, 4096]}, "keyVaultCheckMinimumRSAKeySize": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultMinimumRSAKeySizeValue": {"type": "integer", "defaultValue": 2048, "allowedValues": [2048, 3072, 4096]}, "keyVaultArmRbac": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultHmsPurgeProtection": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultCertificatesPeriod": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "keyVaultCertValidPeriod": {"type": "integer", "defaultValue": 12}, "keyVaultHmsKeysExpiration": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keysValidPeriod": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keysValidityInDays": {"type": "integer", "defaultValue": 90}, "secretsValidPeriod": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "secretsValidityInDays": {"type": "integer", "defaultValue": 90}, "keyVaultCertKeyTypes": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "keyVaultEllipticCurve": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "keyVaultCryptographicType": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keysActive": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keysActiveInDays": {"type": "integer", "defaultValue": 90}, "keysCurveNames": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "secretsActiveInDays": {"type": "integer", "defaultValue": 90}, "secretsActive": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultSecretContentType": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultNonIntegratedCa": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "keyVaultNonIntegratedCaValue": {"type": "string", "defaultValue": "", "metadata": {"displayName": "The common name of the certificate authority", "description": "The common name (CN) of the Certificate Authority (CA) provider. For example, for an issuer CN = Contoso, OU = .., DC = .., you can specify Contoso"}}, "keyVaultIntegratedCa": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "keyVaultIntegratedCaValue": {"type": "array", "defaultValue": ["<PERSON><PERSON><PERSON><PERSON>", "GlobalSign"]}, "keyVaultHsmMinimumDaysBeforeExpiration": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultHsmMinimumDaysBeforeExpirationValue": {"type": "integer", "defaultValue": 90}, "keyVaultHmsCurveNames": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "keyVaultHmsCurveNamesValue": {"type": "array", "defaultValue": ["P-256", "P-256K", "P-384", "P-521"]}, "keyVaultCertificateNotExpireWithinSpecifiedNumberOfDays": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["audit", "Audit", "deny", "<PERSON><PERSON>", "disabled", "Disabled"]}, "keyVaultCertificateNotExpireWithinSpecifiedNumberOfDaysValue": {"type": "integer", "defaultValue": 90}}, "policyDefinitions": [{"policyDefinitionReferenceId": "KvSoftDelete", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1e66c121-a66a-4b1f-9b83-0fd99bf0fc2d", "parameters": {"effect": {"value": "[parameters('effectKvSoftDelete')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KvPurgeProtection", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0b60c0b2-2dc2-4e1c-b5c9-abbed971de53", "parameters": {"effect": {"value": "[parameters('effectKvPurgeProtection')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KvSecretsExpire", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/98728c90-32c7-4049-8429-847dc0f4fe37", "parameters": {"effect": {"value": "[parameters('effectKvSecretsExpire')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KvKeysExpire", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/152b15f7-8e1f-4c1f-ab71-8c010ba5dbc0", "parameters": {"effect": {"value": "[parameters('effectKvKeysExpire')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KvFirewallEnabled", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/55615ac9-af46-4a59-874e-391cc3dfb490", "parameters": {"effect": {"value": "[parameters('effectKvFirewallEnabled')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KvCertLifetime", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/12ef42cb-9903-4e39-9c26-422d29570417", "parameters": {"effect": {"value": "[parameters('effectKvCertLifetime')]"}, "maximumPercentageLife": {"value": "[parameters('maximumCertLifePercentageLife')]"}, "minimumDaysBeforeExpiry": {"value": "[parameters('minimumCertLifeDaysBeforeExpiry')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KvKeysLifetime", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5ff38825-c5d8-47c5-b70e-069a21955146", "parameters": {"effect": {"value": "[parameters('effectKvKeysLifetime')]"}, "minimumDaysBeforeExpiration": {"value": "[parameters('minimumKeysLifeDaysBeforeExpiry')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KvSecretsLifetime", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b0eb591a-5e70-4534-a8bf-04b9c489584a", "parameters": {"effect": {"value": "[parameters('effectKvSecretsLifetime')]"}, "minimumDaysBeforeExpiration": {"value": "[parameters('minimumSecretsLifeDaysBeforeExpiry')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-RSA-Keys-without-MinCertSize", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/cee51871-e572-4576-855c-047c820360f0", "parameters": {"effect": {"value": "[parameters('keyVaultCheckMinimumRSACertificateSize')]"}, "minimumRSAKeySize": {"value": "[parameters('keyVaultMinimumRSACertificateSizeValue')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-keyVaultManagedHsm-RSA-Keys-without-MinKeySize", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/86810a98-8e91-4a44-8386-ec66d0de5d57", "parameters": {"effect": {"value": "[parameters('keyVaultManagedHsmCheckMinimumRSAKeySize')]"}, "minimumRSAKeySize": {"value": "[parameters('keyVaultManagedHsmMinimumRSAKeySizeValue')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-RSA-Keys-without-MinKeySize", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/82067dbb-e53b-4e06-b631-546d197452d9", "parameters": {"effect": {"value": "[parameters('keyVaultCheckMinimumRSAKeySize')]"}, "minimumRSAKeySize": {"value": "[parameters('keyVaultMinimumRSAKeySizeValue')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-K<PERSON>-without-ArmRbac", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/12d4fa5e-1f9f-4c21-97a9-b99b3c6611b5", "parameters": {"effect": {"value": "[parameters('keyVaultArmRbac')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Hms-PurgeProtection", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c39ba22d-4428-4149-b981-70acb31fc383", "parameters": {"effect": {"value": "[parameters('keyVaultHmsPurgeProtection')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Cert-Period", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a075868-4c26-42ef-914c-5bc007359560", "parameters": {"effect": {"value": "[parameters('keyVaultCertificatesPeriod')]"}, "maximumValidityInMonths": {"value": "[parameters('keyVaultCertValidPeriod')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Hms-Key-Expire", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1d478a74-21ba-4b9f-9d8f-8e6fced0eec5", "parameters": {"effect": {"value": "[parameters('keyVaultHmsKeysExpiration')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-<PERSON><PERSON>-Keys-Expire", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/49a22571-d204-4c91-a7b6-09b1a586fbc9", "parameters": {"effect": {"value": "[parameters('keysValidPeriod')]"}, "maximumValidityInDays": {"value": "[parameters('keysValidityInDays')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Secrets-ValidityDays", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/342e8053-e12e-4c44-be01-c3c2f318400f", "parameters": {"effect": {"value": "[parameters('secretsValidPeriod')]"}, "maximumValidityInDays": {"value": "[parameters('secretsValidityInDays')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Key-Types", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1151cede-290b-4ba0-8b38-0ad145ac888f", "parameters": {"effect": {"value": "[parameters('keyVaultCertKeyTypes')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Elliptic-Curve", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bd78111f-4953-4367-9fd5-7e08808b54bf", "parameters": {"effect": {"value": "[parameters('keyVaultEllipticCurve')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Cryptographic-Type", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/75c4f823-d65c-4f29-a733-01d0077fdbcb", "parameters": {"effect": {"value": "[parameters('keyVaultCryptographicType')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Key-Active", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c26e4b24-cf98-4c67-b48b-5a25c4c69eb9", "parameters": {"effect": {"value": "[parameters('keysActive')]"}, "maximumValidityInDays": {"value": "[parameters('keysActiveInDays')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Curve-Names", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ff25f3c8-b739-4538-9d07-3d6d25cfb255", "parameters": {"effect": {"value": "[parameters('keysCurveNames')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-KV-Secret-ActiveDays", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e8d99835-8a06-45ae-a8e0-87a91941ccfe", "parameters": {"effect": {"value": "[parameters('secretsActive')]"}, "maximumValidityInDays": {"value": "[parameters('secretsActiveInDays')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Kv-Secret-Content-Type", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/75262d3e-ba4a-4f43-85f8-9f72c090e5e3", "parameters": {"effect": {"value": "[parameters('keyVaultSecretContentType')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Kv-Non-Integrated-Ca", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a22f4a40-01d3-4c7d-8071-da157eeff341", "parameters": {"effect": {"value": "[parameters('keyVaultNonIntegratedCa')]"}, "caCommonName": {"value": "[parameters('keyVaultNonIntegratedCaValue')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Kv-Integrated-Ca", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8e826246-c976-48f6-b03e-619bb92b3d82", "parameters": {"effect": {"value": "[parameters('keyVaultIntegratedCa')]"}, "allowedCAs": {"value": "[parameters('keyVaultIntegratedCaValue')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Kv-Hsm-MinimumDays-Before-Expiration", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ad27588c-0198-4c84-81ef-08efd0274653", "parameters": {"effect": {"value": "[parameters('keyVaultHsmMinimumDaysBeforeExpiration')]"}, "minimumDaysBeforeExpiration": {"value": "[parameters('keyVaultHsmMinimumDaysBeforeExpirationValue')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Kv-Hsm-Curve-Names", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e58fd0c1-feac-4d12-92db-0a7e9421f53e", "parameters": {"effect": {"value": "[parameters('keyVaultHmsCurveNames')]"}, "allowedECNames": {"value": "[parameters('keyVaultHmsCurveNamesValue')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Kv-Cert-Expiration-Within-Specific-Number-Days", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f772fb64-8e40-40ad-87bc-7706e1949427", "parameters": {"effect": {"value": "[parameters('keyVaultCertificateNotExpireWithinSpecifiedNumberOfDays')]"}, "daysToExpire": {"value": "[parameters('keyVaultCertificateNotExpireWithinSpecifiedNumberOfDaysValue')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}