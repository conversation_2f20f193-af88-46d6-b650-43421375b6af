{"name": "Deny-Storage-ResourceAccessRulesResourceId", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Resource Access Rules resource IDs should be restricted for Storage Accounts", "description": "Azure Storage accounts should restrict the resource access rule for service-level network ACLs to services from a specific Azure subscription. Enforce this for increased data exfiltration protection.", "metadata": {"version": "1.0.0", "category": "Storage", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"count": {"field": "Microsoft.Storage/storageAccounts/networkAcls.resourceAccessRules[*]"}, "greater": 0}, {"count": {"field": "Microsoft.Storage/storageAccounts/networkAcls.resourceAccessRules[*]", "where": {"value": "[split(current('Microsoft.Storage/storageAccounts/networkAcls.resourceAccessRules[*].resourceId'), '/')[2]]", "equals": "*"}}, "greater": 0}]}, "then": {"effect": "[parameters('effect')]"}}}}