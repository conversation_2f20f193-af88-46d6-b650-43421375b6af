{"name": "DenyAction-ActivityLogs", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "DenyAction implementation on Activity Logs", "description": "This is a DenyAction implementation policy on Activity Logs.", "metadata": {"deprecated": false, "version": "1.0.0", "category": "Monitoring", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.Resources/subscriptions/providers/diagnosticSettings"}, "then": {"effect": "denyAction", "details": {"actionNames": ["delete"]}}}}}