{"name": "Enforce-Guardrails-BotService", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Bot Service", "description": "This policy initiative is a group of policies that ensures Bot Service is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "Bot Service", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"botServiceValidUri": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "audit", "<PERSON><PERSON>", "deny", "Disabled", "disabled"]}, "botServiceIsolatedMode": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "audit", "<PERSON><PERSON>", "deny", "Disabled", "disabled"]}, "botServiceLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "botServicePrivateLink": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-BotService-Valid-<PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6164527b-e1ee-4882-8673-572f425f5e0a", "parameters": {"effect": {"value": "[parameters('botServiceValidUri')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-BotService-Isolated-Mode", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/52152f42-0dda-40d9-976e-abb1acdd611e", "parameters": {"effect": {"value": "[parameters('botServiceIsolatedMode')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-BotService-Local-Auth", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ffea632e-4e3a-4424-bf78-10e179bb2e1a", "parameters": {"effect": {"value": "[parameters('botServiceLocalAuth')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Audit-BotService-Private-Link", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ad5621d6-a877-4407-aa93-a950b428315e", "parameters": {"effect": {"value": "[parameters('botServicePrivateLink')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}