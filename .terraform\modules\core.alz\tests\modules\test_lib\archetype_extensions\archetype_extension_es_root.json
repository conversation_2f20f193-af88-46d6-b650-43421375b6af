{"extend_es_root": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Deploy-SQL-Auditing", "Deploy-HITRUST-HIPAA"], "policy_definitions": [], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"listOfAllowedLocations": ["eastus", "eastus2", "west<PERSON>", "northcentralus", "southcentralus"]}, "Deny-RSG-Locations": {"listOfAllowedLocations": ["eastus", "eastus2", "west<PERSON>", "northcentralus", "southcentralus"]}, "Deploy-SQL-Auditing": {"retentionDays": "10", "storageAccountsResourceGroup": ""}, "Deploy-HITRUST-HIPAA": {"CertificateThumbprints": "", "DeployDiagnosticSettingsforNetworkSecurityGroupsrgName": "", "DeployDiagnosticSettingsforNetworkSecurityGroupsstoragePrefix": "", "installedApplicationsOnWindowsVM": ""}}, "access_control": {}}}}