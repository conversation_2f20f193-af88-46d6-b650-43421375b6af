# Support

## How to file issues and get help

This project uses GitHub Issues to track bugs and feature requests. Please search the existing issues before filing new issues to avoid duplicates.  For new issues, file your bug or feature request as a new Issue.

Issues can be created and searched through for existing issues here: [https://github.com/Azure/terraform-azurerm-caf-enterprise-scale/issues](https://github.com/Azure/terraform-azurerm-caf-enterprise-scale/issues)

Please provide as much information as possible when filing an issue. Include screenshots or correlation IDs if possible (please redact any sensitive information).

We may also ask you to provide some insights from some debug outputs from Terraform via the `TF_LOG` environment variable.

We may ask you to create an Azure support request once we have triaged the issue following the process documented [here](https://learn.microsoft.com/azure/azure-portal/supportability/how-to-create-azure-support-request)

## Microsoft Support Policy

If issues are encountered when deploying this Terraform module users will be able to engage Microsoft support via their usual channels. Please provide correlation IDs where possible when contacting support to be able to investigate the issue effectively and in a timely fashion.

Following list of issues are within the scope of Microsoft support:

- Underlying Resource or Resource Provider issues when deploying modules (e.g. Management Groups, Policies, Log Analytics Workspaces, RBAC, Virtual Networks) for any deployment failures.

Any issues that are deemed outside of the above list by Microsoft support and/or requires bugfix in the module or code in the repo, Microsoft support will redirect user to file the issue on GitHub.

Project maintainers and community aim to get issues resolved in timely fashion as per community support
policy of this repo.

## Community Support Policy

Project maintainers will aim to respond within 3 business days to get a meaningful response for any new issues.
