{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deploy-SQL-Threat", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This policy ensures that Threat Detection is enabled on SQL Servers.", "displayName": "Deploy Threat Detection on SQL servers", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/36d49e87-48c4-4f2e-beed-ba4ed02b71f5", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Threat Detection {enforcementMode} be deployed on SQL servers."}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}}