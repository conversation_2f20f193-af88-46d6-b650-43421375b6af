{"name": "Enforce-Guardrails-MySQL", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for MySQL", "description": "This policy initiative is a group of policies that ensures MySQL is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "MySQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"mySqlInfraEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "mySqlAdvThreatProtection": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Dine-MySql-Adv-Threat-Protection", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/80ed5239-4122-41ed-b54a-6f1fa7552816", "parameters": {"effect": {"value": "[parameters('mySqlAdvThreatProtection')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-MySql-Infra-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3a58212a-c829-4f13-9872-6371df2fd0b4", "parameters": {"effect": {"value": "[parameters('mySqlInfraEncryption')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}