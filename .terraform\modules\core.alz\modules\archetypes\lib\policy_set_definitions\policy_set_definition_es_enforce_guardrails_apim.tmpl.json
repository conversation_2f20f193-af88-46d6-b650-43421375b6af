{"name": "Enforce-Guardrails-APIM", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for API Management", "description": "This policy initiative is a group of policies that ensures API Management is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "API Management", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"apiSubscriptionScope": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "minimumApiVersion": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "apimSkuVnet": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "apimDisablePublicNetworkAccess": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "apimApiBackendCertValidation": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "apimDirectApiEndpoint": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "apimCallApiAuthn": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "apimEncryptedProtocols": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "apimVnetUsage": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "apimSecrets": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "apimTls": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>-without-Kv", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f1cc7827-022c-473e-836e-5a51cae0b249", "parameters": {"effect": {"value": "[parameters('apimSecrets')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>-without-Vnet", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ef619a2c-cc4d-4d03-b2ba-8c94a834d85b", "parameters": {"effect": {"value": "[parameters('apimVnetUsage')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-APIM-TLS", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/Deny-APIM-TLS", "parameters": {"effect": {"value": "[parameters('apimTls')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Apim-Protocols", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ee7495e7-3ba7-40b6-bfee-c29e22cc75d4", "parameters": {"effect": {"value": "[parameters('apimEncryptedProtocols')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c15dcc82-b93c-4dcb-9332-fbf121685b54", "parameters": {"effect": {"value": "[parameters('apimCallApiAuthn')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Direct-Endpoint", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b741306c-968e-4b67-b916-5675e5c709f4", "parameters": {"effect": {"value": "[parameters('apimDirectApiEndpoint')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Apim-Cert-Validation", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/92bb331d-ac71-416a-8c91-02f2cb734ce4", "parameters": {"effect": {"value": "[parameters('apimApiBackendCertValidation')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON>m-Public-NetworkAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7ca8c8ac-3a6e-493d-99ba-c5fa35347ff2", "parameters": {"effect": {"value": "[parameters('apimDisablePublicNetworkAccess')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Apim-Sku-Vnet", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/73ef9241-5d81-4cd4-b483-8443d1730fe5", "parameters": {"effect": {"value": "[parameters('apimSkuVnet')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Apim-Version", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/549814b6-3212-4203-bdc8-1548d342fb67", "parameters": {"effect": {"value": "[parameters('minimumApiVersion')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-Api-subscription-scope", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3aa03346-d8c5-4994-a5bc-7652c2a2aef1", "parameters": {"effect": {"value": "[parameters('apiSubscriptionScope')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}