{"name": "Deny-MachineLearning-Compute-VmSize", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Limit allowed vm sizes for Azure Machine Learning compute clusters and compute instances", "description": "Limit allowed vm sizes for Azure Machine Learning compute clusters and compute instances.", "metadata": {"version": "1.0.0", "category": "Budget", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"], "defaultValue": "<PERSON><PERSON>"}, "allowedVmSizes": {"type": "Array", "metadata": {"displayName": "Allowed VM Sizes for Aml Compute Clusters and Instances", "description": "Specifies the allowed VM Sizes for Aml Compute Clusters and Instances"}, "defaultValue": ["Standard_D1_v2", "Standard_D2_v2", "Standard_D3_v2", "Standard_D4_v2", "Standard_D11_v2", "Standard_D12_v2", "Standard_D13_v2", "Standard_D14_v2", "Standard_DS1_v2", "Standard_DS2_v2", "Standard_DS3_v2", "Standard_DS4_v2", "Standard_DS5_v2", "Standard_DS11_v2", "Standard_DS12_v2", "Standard_DS13_v2", "Standard_DS14_v2", "Standard_M8-2ms", "Standard_M8-4ms", "Standard_M8ms", "Standard_M16-4ms", "Standard_M16-8ms", "Standard_M16ms", "Standard_M32-8ms", "Standard_M32-16ms", "Standard_M32ls", "Standard_M32ms", "Standard_M32ts", "Standard_M64-16ms", "Standard_M64-32ms", "Standard_M64ls", "Standard_M64ms", "Standard_M64s", "Standard_M128-32ms", "Standard_M128-64ms", "Standard_M128ms", "Standard_M128s", "Standard_M64", "Standard_M64m", "Standard_M128", "Standard_M128m", "Standard_D1", "Standard_D2", "Standard_D3", "Standard_D4", "Standard_D11", "Standard_D12", "Standard_D13", "Standard_D14", "Standard_DS15_v2", "Standard_NV6", "Standard_NV12", "Standard_NV24", "Standard_F2s_v2", "Standard_F4s_v2", "Standard_F8s_v2", "Standard_F16s_v2", "Standard_F32s_v2", "Standard_F64s_v2", "Standard_F72s_v2", "Standard_NC6s_v3", "Standard_NC12s_v3", "Standard_NC24rs_v3", "Standard_NC24s_v3", "Standard_NC6", "Standard_NC12", "Standard_NC24", "Standard_NC24r", "Standard_ND6s", "Standard_ND12s", "Standard_ND24rs", "Standard_ND24s", "Standard_NC6s_v2", "Standard_NC12s_v2", "Standard_NC24rs_v2", "Standard_NC24s_v2", "Standard_ND40rs_v2", "Standard_NV12s_v3", "Standard_NV24s_v3", "Standard_NV48s_v3"]}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.MachineLearningServices/workspaces/computes"}, {"field": "Microsoft.MachineLearningServices/workspaces/computes/computeType", "in": ["AmlCompute", "ComputeInstance"]}, {"field": "Microsoft.MachineLearningServices/workspaces/computes/vmSize", "notIn": "[parameters('allowedVmSizes')]"}]}, "then": {"effect": "[parameters('effect')]"}}}}