---
parameters:
  - name: run_type
    type: string
    default: default

steps:
  - task: Bash@3
    displayName: "Install Terraform"
    inputs:
      targetType: "inline"
      script: "make tf-install"

  - task: GoTool@0
    displayName: "Install Go"
    inputs:
      version: "1.22.3"
    condition: and(succeeded(), eq('${{ parameters.run_type }}', 'unit'))

  - task: Bash@3
    displayName: "Prepare Terraform Environment"
    inputs:
      targetType: "inline"
      script: "make tf-prepare"
    env:
      ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
