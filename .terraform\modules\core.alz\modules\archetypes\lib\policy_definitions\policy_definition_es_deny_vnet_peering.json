{"name": "Deny-VNet-Peering", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Deny vNet peering ", "description": "This policy denies the creation of vNet Peerings under the assigned scope.", "metadata": {"version": "1.0.1", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings"}, "then": {"effect": "[parameters('effect')]"}}}}