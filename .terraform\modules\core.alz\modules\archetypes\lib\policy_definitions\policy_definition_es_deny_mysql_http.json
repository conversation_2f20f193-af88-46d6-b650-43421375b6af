{"name": "Deny-MySql-http", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "MySQL database servers enforce SSL connections.", "description": "Azure Database for MySQL supports connecting your Azure Database for MySQL server to client applications using Secure Sockets Layer (SSL). Enforcing SSL connections between your database server and your client applications helps protect against 'man in the middle' attacks by encrypting the data stream between the server and your application. This configuration enforces that SSL is always enabled for accessing your database server.", "metadata": {"version": "1.1.0", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "minimalTlsVersion": {"type": "String", "defaultValue": "TLS1_2", "allowedValues": ["TLS1_2", "TLS1_0", "TLS1_1", "TLSEnforcementDisabled"], "metadata": {"displayName": "Select version minimum TLS for MySQL server", "description": "Select version  minimum TLS version Azure Database for MySQL server to enforce"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.DBforMySQL/servers"}, {"anyOf": [{"field": "Microsoft.DBforMySQL/servers/sslEnforcement", "exists": "false"}, {"field": "Microsoft.DBforMySQL/servers/sslEnforcement", "notEquals": "Enabled"}, {"field": "Microsoft.DBforMySQL/servers/minimalTlsVersion", "less": "[parameters('minimalTlsVersion')]"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}