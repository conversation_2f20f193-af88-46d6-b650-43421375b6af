{"name": "Deploy-HITRUST-HIPAA", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This assignment includes audit and virtual machine extension deployment policies that address a subset of HITRUST/HIPAA controls. Additional policies will be added in upcoming releases. For more information, visit https://aka.ms/hipaa-blueprint.", "displayName": "Assign policies for HITRUST and HIPAA controls", "notScopes": [], "parameters": {"installedApplicationsOnWindowsVM": {"value": ""}, "DeployDiagnosticSettingsforNetworkSecurityGroupsstoragePrefix": {"value": ""}, "DeployDiagnosticSettingsforNetworkSecurityGroupsrgName": {"value": ""}, "CertificateThumbprints": {"value": ""}}, "policyDefinitionId": "/providers/Microsoft.Authorization/policySetDefinitions/a169a624-5599-4385-a696-c8d643089fab", "nonComplianceMessages": [{"message": "HITRUST/HIPAA controls audit and virtual machine extensions {enforcementMode} be deployed."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "SystemAssigned"}}