{"name": "Deploy-Log-Analytics", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "Deploy-Log-Analytics.", "displayName": "Deploy-Log-Analytics", "notScopes": [], "parameters": {"workspaceName": {"value": "${root_scope_id}-la"}, "automationAccountName": {"value": "${root_scope_id}-automation"}, "workspaceRegion": {"value": "${default_location}"}, "automationRegion": {"value": "${default_location}"}, "dataRetention": {"value": "30"}, "sku": {"value": "pergb2018"}, "rgName": {"value": "${root_scope_id}-mgmt"}, "effect": {"value": "DeployIfNotExists"}}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8e3e61b3-0b32-22d5-4edf-55f87fdb5955", "scope": "${current_scope_resource_id}", "enforcementMode": "DoNotEnforce"}, "location": "${default_location}", "identity": {"type": "UserAssigned", "userAssignedIdentities": {"/subscriptions/${subscription_id}/resourceGroups/${umi_resource_group_name}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/${umi_name}": {}}}}