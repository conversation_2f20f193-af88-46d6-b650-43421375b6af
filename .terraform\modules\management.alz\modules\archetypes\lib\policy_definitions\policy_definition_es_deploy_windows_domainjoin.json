{"name": "Deploy-Windows-DomainJoin", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Deploy Windows Domain Join Extension with keyvault configuration", "description": "Deploy Windows Domain Join Extension with keyvault configuration when the extension does not exist on a given windows Virtual Machine", "metadata": {"version": "1.0.0", "category": "Guest Configuration", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"domainUsername": {"type": "String", "metadata": {"displayName": "domainUsername"}}, "domainPassword": {"type": "String", "metadata": {"displayName": "domainPassword"}}, "domainFQDN": {"type": "String", "metadata": {"displayName": "domainFQDN"}}, "domainOUPath": {"type": "String", "metadata": {"displayName": "domainOUPath"}}, "keyVaultResourceId": {"type": "String", "metadata": {"displayName": "keyVaultResourceId"}}, "effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Compute/virtualMachines"}, {"field": "Microsoft.Compute/imagePublisher", "equals": "MicrosoftWindowsServer"}, {"field": "Microsoft.Compute/imageOffer", "equals": "WindowsServer"}, {"field": "Microsoft.Compute/imageSKU", "in": ["2008-R2-SP1", "2008-R2-SP1-smalldisk", "2008-R2-SP1-zhcn", "2012-Datacenter", "2012-datacenter-gensecond", "2012-Datacenter-smalldisk", "2012-datacenter-smalldisk-g2", "2012-Datacenter-zhcn", "2012-datacenter-zhcn-g2", "2012-R2-Datacenter", "2012-r2-datacenter-gensecond", "2012-R2-Datacenter-smalldisk", "2012-r2-datacenter-smalldisk-g2", "2012-R2-Datacenter-zhcn", "2012-r2-datacenter-zhcn-g2", "2016-Datacenter", "2016-datacenter-gensecond", "2016-datacenter-gs", "2016-Datacenter-Server-Core", "2016-datacenter-server-core-g2", "2016-Datacenter-Server-Core-smalldisk", "2016-datacenter-server-core-smalldisk-g2", "2016-Datacenter-smalldisk", "2016-datacenter-smalldisk-g2", "2016-Datacenter-with-Containers", "2016-datacenter-with-containers-g2", "2016-Datacenter-with-RDSH", "2016-Datacenter-zhcn", "2016-datacenter-zhcn-g2", "2019-Datacenter", "2019-Datacenter-Core", "2019-datacenter-core-g2", "2019-Datacenter-Core-smalldisk", "2019-datacenter-core-smalldisk-g2", "2019-Datacenter-Core-with-Containers", "2019-datacenter-core-with-containers-g2", "2019-Datacenter-Core-with-Containers-smalldisk", "2019-datacenter-core-with-containers-smalldisk-g2", "2019-datacenter-gensecond", "2019-datacenter-gs", "2019-Datacenter-smalldisk", "2019-datacenter-smalldisk-g2", "2019-Datacenter-with-Containers", "2019-datacenter-with-containers-g2", "2019-Datacenter-with-Containers-smalldisk", "2019-datacenter-with-containers-smalldisk-g2", "2019-Datacenter-zhcn", "2019-datacenter-zhcn-g2", "Datacenter-Core-1803-with-Containers-smalldisk", "datacenter-core-1803-with-containers-smalldisk-g2", "Datacenter-Core-1809-with-Containers-smalldisk", "datacenter-core-1809-with-containers-smalldisk-g2", "Datacenter-Core-1903-with-Containers-smalldisk", "datacenter-core-1903-with-containers-smalldisk-g2", "datacenter-core-1909-with-containers-smalldisk", "datacenter-core-1909-with-containers-smalldisk-g1", "datacenter-core-1909-with-containers-smalldisk-g2"]}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Compute/virtualMachines/extensions", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/9980e02c-c2be-4d73-94e8-173b1dc7cf3c"], "existenceCondition": {"allOf": [{"field": "Microsoft.Compute/virtualMachines/extensions/type", "equals": "JsonADDomainExtension"}, {"field": "Microsoft.Compute/virtualMachines/extensions/publisher", "equals": "Microsoft.Compute"}]}, "deployment": {"properties": {"mode": "Incremental", "parameters": {"vmName": {"value": "[field('name')]"}, "location": {"value": "[field('location')]"}, "domainUsername": {"reference": {"keyVault": {"id": "[parameters('keyVaultResourceId')]"}, "secretName": "[parameters('domainUsername')]"}}, "domainPassword": {"reference": {"keyVault": {"id": "[parameters('keyVaultResourceId')]"}, "secretName": "[parameters('domainPassword')]"}}, "domainOUPath": {"value": "[parameters('domainOUPath')]"}, "domainFQDN": {"value": "[parameters('domainFQDN')]"}, "keyVaultResourceId": {"value": "[parameters('keyVaultResourceId')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"vmName": {"type": "String"}, "location": {"type": "String"}, "domainUsername": {"type": "String"}, "domainPassword": {"type": "securestring"}, "domainFQDN": {"type": "String"}, "domainOUPath": {"type": "String"}, "keyVaultResourceId": {"type": "String"}}, "variables": {"domainJoinOptions": 3, "vmName": "[parameters('vmName')]"}, "resources": [{"apiVersion": "2015-06-15", "type": "Microsoft.Compute/virtualMachines/extensions", "name": "[concat(variables('vmName'),'/joindomain')]", "location": "[resourceGroup().location]", "properties": {"publisher": "Microsoft.Compute", "type": "JsonADDomainExtension", "typeHandlerVersion": "1.3", "autoUpgradeMinorVersion": true, "settings": {"Name": "[parameters('domainFQDN')]", "User": "[parameters('domainUserName')]", "Restart": "true", "Options": "[variables('domainJoinOptions')]", "OUPath": "[parameters('domainOUPath')]"}, "protectedSettings": {"Password": "[parameters('domainPassword')]"}}}], "outputs": {}}}}}}}}}