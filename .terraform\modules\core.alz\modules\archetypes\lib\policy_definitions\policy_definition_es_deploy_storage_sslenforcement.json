{"name": "Deploy-Storage-sslEnforcement", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Azure Storage deploy a specific min TLS version requirement and enforce SSL/HTTPS ", "description": "Deploy a specific min TLS version requirement and enforce SSL on Azure Storage. Enables secure server to client by enforce minimal Tls Version to secure the connection between your database server and your client applications helps protect against 'man in the middle' attacks by encrypting the data stream between the server and your application. This configuration enforces that SSL is always enabled for accessing your Azure Storage.", "metadata": {"version": "1.3.0", "category": "Storage", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect Azure Storage", "description": "Enable or disable the execution of the policy minimum TLS version Azure STorage"}}, "minimumTlsVersion": {"type": "String", "defaultValue": "TLS1_2", "allowedValues": ["TLS1_2", "TLS1_1", "TLS1_0"], "metadata": {"displayName": "Select TLS version for Azure Storage server", "description": "Select version minimum TLS version Azure STorage to enforce"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"anyOf": [{"field": "Microsoft.Storage/storageAccounts/supportsHttpsTrafficOnly", "notEquals": "true"}, {"field": "Microsoft.Storage/storageAccounts/minimumTlsVersion", "less": "[parameters('minimumTlsVersion')]"}]}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Storage/storageAccounts", "existenceCondition": {"allOf": [{"field": "Microsoft.Storage/storageAccounts/supportsHttpsTrafficOnly", "equals": "true"}, {"field": "Microsoft.Storage/storageAccounts/minimumTlsVersion", "equals": "[parameters('minimumTlsVersion')]"}]}, "name": "current", "roleDefinitionIds": ["/providers/microsoft.authorization/roleDefinitions/17d1049b-9a84-46fb-8f53-869881c3d3ab"], "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"resourceName": {"type": "String"}, "minimumTlsVersion": {"type": "String"}, "location": {"type": "String"}}, "variables": {}, "resources": [{"type": "Microsoft.Storage/storageAccounts", "apiVersion": "2019-06-01", "name": "[concat(parameters('resourceName'))]", "location": "[parameters('location')]", "properties": {"supportsHttpsTrafficOnly": true, "minimumTlsVersion": "[parameters('minimumTlsVersion')]"}}], "outputs": {}}, "parameters": {"resourceName": {"value": "[field('name')]"}, "minimumTlsVersion": {"value": "[parameters('minimumTlsVersion')]"}, "location": {"value": "[field('location')]"}}}}}}}}}