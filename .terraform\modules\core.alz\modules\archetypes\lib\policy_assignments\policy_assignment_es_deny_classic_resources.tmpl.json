{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deny-Classic-Resources", "dependsOn": [], "properties": {"description": "Denies deployment of classic resource types under the assigned scope.", "displayName": "Deny the deployment of classic resources", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6c112d4e-5bc7-47ae-a041-ea2d9dccd749", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Classic resources {enforcementMode} not be deployed."}], "parameters": {"listOfResourceTypesNotAllowed": {"value": ["Microsoft.ClassicCompute/capabilities", "Microsoft.ClassicCompute/checkDomainNameAvailability", "Microsoft.ClassicCompute/domainNames", "Microsoft.ClassicCompute/domainNames/capabilities", "Microsoft.ClassicCompute/domainNames/internalLoadBalancers", "Microsoft.ClassicCompute/domainNames/serviceCertificates", "Microsoft.ClassicCompute/domainNames/slots", "Microsoft.ClassicCompute/domainNames/slots/roles", "Microsoft.ClassicCompute/domainNames/slots/roles/metricDefinitions", "Microsoft.ClassicCompute/domainNames/slots/roles/metrics", "Microsoft.ClassicCompute/moveSubscriptionResources", "Microsoft.ClassicCompute/operatingSystemFamilies", "Microsoft.ClassicCompute/operatingSystems", "Microsoft.ClassicCompute/operations", "Microsoft.ClassicCompute/operationStatuses", "Microsoft.ClassicCompute/quotas", "Microsoft.ClassicCompute/resourceTypes", "Microsoft.ClassicCompute/validateSubscriptionMoveAvailability", "Microsoft.ClassicCompute/virtualMachines", "Microsoft.ClassicCompute/virtualMachines/diagnosticSettings", "Microsoft.ClassicCompute/virtualMachines/metricDefinitions", "Microsoft.ClassicCompute/virtualMachines/metrics", "Microsoft.ClassicInfrastructureMigrate/classicInfrastructureResources", "Microsoft.ClassicNetwork/capabilities", "Microsoft.ClassicNetwork/expressRouteCrossConnections", "Microsoft.ClassicNetwork/expressRouteCrossConnections/peerings", "Microsoft.ClassicNetwork/gatewaySupportedDevices", "Microsoft.ClassicNetwork/networkSecurityGroups", "Microsoft.ClassicNetwork/operations", "Microsoft.ClassicNetwork/quotas", "Microsoft.ClassicNetwork/reservedIps", "Microsoft.ClassicNetwork/virtualNetworks", "Microsoft.ClassicNetwork/virtualNetworks/remoteVirtualNetworkPeeringProxies", "Microsoft.ClassicNetwork/virtualNetworks/virtualNetworkPeerings", "Microsoft.ClassicStorage/capabilities", "Microsoft.ClassicStorage/checkStorageAccountAvailability", "Microsoft.ClassicStorage/disks", "Microsoft.ClassicStorage/images", "Microsoft.ClassicStorage/operations", "Microsoft.ClassicStorage/osImages", "Microsoft.ClassicStorage/osPlatformImages", "Microsoft.ClassicStorage/publicImages", "Microsoft.ClassicStorage/quotas", "Microsoft.ClassicStorage/storageAccounts", "Microsoft.ClassicStorage/storageAccounts/blobServices", "Microsoft.ClassicStorage/storageAccounts/fileServices", "Microsoft.ClassicStorage/storageAccounts/metricDefinitions", "Microsoft.ClassicStorage/storageAccounts/metrics", "Microsoft.ClassicStorage/storageAccounts/queueServices", "Microsoft.ClassicStorage/storageAccounts/services", "Microsoft.ClassicStorage/storageAccounts/services/diagnosticSettings", "Microsoft.ClassicStorage/storageAccounts/services/metricDefinitions", "Microsoft.ClassicStorage/storageAccounts/services/metrics", "Microsoft.ClassicStorage/storageAccounts/tableServices", "Microsoft.ClassicStorage/storageAccounts/vmImages", "Microsoft.ClassicStorage/vmImages", "Microsoft.ClassicSubscription/operations"]}, "effect": {"value": "<PERSON><PERSON>"}}, "scope": "${current_scope_resource_id}", "notScopes": []}, "location": "${default_location}", "identity": {"type": "None"}}