{"name": "Deploy-Diagnostics-Databricks", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "[Deprecated]: Deploy Diagnostic Settings for Databricks to Log Analytics workspace", "description": "Deploys the diagnostic settings for Databricks to stream to a Log Analytics workspace when any Databricks which is missing this diagnostic settings is created or updated. This policy is superseded by built-in initiative https://www.azadvertizer.net/azpolicyinitiativesadvertizer/0884adba-2312-4468-abeb-5422caed1038.html.", "metadata": {"deprecated": true, "version": "1.3.0-deprecated", "category": "Monitoring", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"logAnalytics": {"type": "String", "metadata": {"displayName": "Log Analytics workspace", "description": "Select Log Analytics workspace from dropdown list. If this workspace is outside of the scope of the assignment you must manually grant 'Log Analytics Contributor' permissions (or similar) to the policy assignment's principal ID.", "strongType": "omsWorkspace"}}, "effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "profileName": {"type": "String", "defaultValue": "setbypolicy", "metadata": {"displayName": "Profile name", "description": "The diagnostic settings profile name"}}, "logsEnabled": {"type": "String", "defaultValue": "True", "allowedValues": ["True", "False"], "metadata": {"displayName": "Enable logs", "description": "Whether to enable logs stream to the Log Analytics workspace - True or False"}}}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.Databricks/workspaces"}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Insights/diagnosticSettings", "name": "[parameters('profileName')]", "existenceCondition": {"allOf": [{"field": "Microsoft.Insights/diagnosticSettings/logs.enabled", "equals": "true"}, {"field": "Microsoft.Insights/diagnosticSettings/workspaceId", "equals": "[parameters('logAnalytics')]"}]}, "roleDefinitionIds": ["/providers/microsoft.authorization/roleDefinitions/749f88d5-cbae-40b8-bcfc-e573ddc772fa", "/providers/microsoft.authorization/roleDefinitions/92aaf0da-9dab-42b6-94a3-d43ce8d16293"], "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"resourceName": {"type": "String"}, "logAnalytics": {"type": "String"}, "location": {"type": "String"}, "profileName": {"type": "String"}, "logsEnabled": {"type": "String"}}, "variables": {}, "resources": [{"type": "Microsoft.Databricks/workspaces/providers/diagnosticSettings", "apiVersion": "2017-05-01-preview", "name": "[concat(parameters('resourceName'), '/', 'Microsoft.Insights/', parameters('profileName'))]", "location": "[parameters('location')]", "dependsOn": [], "properties": {"workspaceId": "[parameters('logAnalytics')]", "logs": [{"category": "dbfs", "enabled": "[parameters('logsEnabled')]"}, {"category": "clusters", "enabled": "[parameters('logsEnabled')]"}, {"category": "accounts", "enabled": "[parameters('logsEnabled')]"}, {"category": "jobs", "enabled": "[parameters('logsEnabled')]"}, {"category": "notebook", "enabled": "[parameters('logsEnabled')]"}, {"category": "ssh", "enabled": "[parameters('logsEnabled')]"}, {"category": "workspace", "enabled": "[parameters('logsEnabled')]"}, {"category": "secrets", "enabled": "[parameters('logsEnabled')]"}, {"category": "sqlPermissions", "enabled": "[parameters('logsEnabled')]"}, {"category": "instancePools", "enabled": "[parameters('logsEnabled')]"}, {"category": "sqlanalytics", "enabled": "[parameters('logsEnabled')]"}, {"category": "genie", "enabled": "[parameters('logsEnabled')]"}, {"category": "globalInitScripts", "enabled": "[parameters('logsEnabled')]"}, {"category": "iamRole", "enabled": "[parameters('logsEnabled')]"}, {"category": "mlflowExperiment", "enabled": "[parameters('logsEnabled')]"}, {"category": "featureStore", "enabled": "[parameters('logsEnabled')]"}, {"category": "RemoteHistoryService", "enabled": "[parameters('logsEnabled')]"}, {"category": "mlflowAcledArtifact", "enabled": "[parameters('logsEnabled')]"}, {"category": "databrickssql", "enabled": "[parameters('logsEnabled')]"}, {"category": "deltaPipelines", "enabled": "[parameters('logsEnabled')]"}, {"category": "modelRegistry", "enabled": "[parameters('logsEnabled')]"}, {"category": "repos", "enabled": "[parameters('logsEnabled')]"}, {"category": "unityCatalog", "enabled": "[parameters('logsEnabled')]"}, {"category": "gitCredentials", "enabled": "[parameters('logsEnabled')]"}, {"category": "webTerminal", "enabled": "[parameters('logsEnabled')]"}, {"category": "serverlessRealTimeInference", "enabled": "[parameters('logsEnabled')]"}, {"category": "clusterLibraries", "enabled": "[parameters('logsEnabled')]"}, {"category": "partnerHub", "enabled": "[parameters('logsEnabled')]"}, {"category": "clamAVScan", "enabled": "[parameters('logsEnabled')]"}, {"category": "capsule8Dataplane", "enabled": "[parameters('logsEnabled')]"}]}}], "outputs": {}}, "parameters": {"logAnalytics": {"value": "[parameters('logAnalytics')]"}, "location": {"value": "[field('location')]"}, "resourceName": {"value": "[field('name')]"}, "profileName": {"value": "[parameters('profileName')]"}, "logsEnabled": {"value": "[parameters('logsEnabled')]"}}}}}}}}}