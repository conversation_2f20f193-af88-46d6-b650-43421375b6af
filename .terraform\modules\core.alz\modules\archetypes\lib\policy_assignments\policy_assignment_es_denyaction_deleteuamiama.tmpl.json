{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "DenyAction-DeleteUAMIAMA", "dependsOn": [], "properties": {"description": "This policy provides a safeguard against accidental removal of the User Assigned Managed Identity used by AMA by blocking delete calls using deny action effect.", "displayName": "Do not allow deletion of the User Assigned Managed Identity used by AMA", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/DenyAction-DeleteResources", "enforcementMode": "<PERSON><PERSON><PERSON>", "parameters": {"effect": {"value": "DenyAction"}, "resourceName": {"value": "defaultString3"}, "resourceType": {"value": "defaultString4"}}, "scope": "${current_scope_resource_id}", "notScopes": []}, "location": "${default_location}", "identity": {"type": "None"}}