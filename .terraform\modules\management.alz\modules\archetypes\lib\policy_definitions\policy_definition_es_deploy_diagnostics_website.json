{"name": "Deploy-Diagnostics-Website", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "[Deprecated]: Deploy Diagnostic Settings for App Service to Log Analytics workspace", "description": "Deploys the diagnostic settings for Web App to stream to a Log Analytics workspace when any Web App which is missing this diagnostic settings is created or updated. This policy is superseded by built-in initiative https://www.azadvertizer.net/azpolicyinitiativesadvertizer/0884adba-2312-4468-abeb-5422caed1038.html.", "metadata": {"deprecated": true, "version": "1.2.0-deprecated", "category": "Monitoring", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"logAnalytics": {"type": "String", "metadata": {"displayName": "Log Analytics workspace", "description": "Select Log Analytics workspace from dropdown list. If this workspace is outside of the scope of the assignment you must manually grant 'Log Analytics Contributor' permissions (or similar) to the policy assignment's principal ID.", "strongType": "omsWorkspace"}}, "effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "profileName": {"type": "String", "defaultValue": "setbypolicy", "metadata": {"displayName": "Profile name", "description": "The diagnostic settings profile name"}}, "metricsEnabled": {"type": "String", "defaultValue": "True", "allowedValues": ["True", "False"], "metadata": {"displayName": "Enable metrics", "description": "Whether to enable metrics stream to the Log Analytics workspace - True or False"}}, "logsEnabled": {"type": "String", "defaultValue": "True", "allowedValues": ["True", "False"], "metadata": {"displayName": "Enable logs", "description": "Whether to enable logs stream to the Log Analytics workspace - True or False"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Web/sites"}, {"value": "[field('kind')]", "notContains": "functionapp"}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Insights/diagnosticSettings", "name": "[parameters('profileName')]", "existenceCondition": {"allOf": [{"field": "Microsoft.Insights/diagnosticSettings/logs.enabled", "equals": "[parameters('logsEnabled')]"}, {"field": "Microsoft.Insights/diagnosticSettings/metrics.enabled", "equals": "[parameters('metricsEnabled')]"}, {"field": "Microsoft.Insights/diagnosticSettings/workspaceId", "equals": "[parameters('logAnalytics')]"}]}, "roleDefinitionIds": ["/providers/microsoft.authorization/roleDefinitions/749f88d5-cbae-40b8-bcfc-e573ddc772fa", "/providers/microsoft.authorization/roleDefinitions/92aaf0da-9dab-42b6-94a3-d43ce8d16293"], "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"resourceName": {"type": "String"}, "logAnalytics": {"type": "String"}, "location": {"type": "String"}, "profileName": {"type": "String"}, "metricsEnabled": {"type": "String"}, "logsEnabled": {"type": "String"}, "serverFarmId": {"type": "String"}}, "variables": {"logs": {"premiumTierLogs": [{"category": "AppServiceAntivirusScanAuditLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceHTTPLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceConsoleLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceAppLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceFileAuditLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceAuditLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceIPSecAuditLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServicePlatformLogs", "enabled": "[parameters('logsEnabled')]"}], "otherTierLogs": [{"category": "AppServiceHTTPLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceConsoleLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceAppLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceAuditLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServiceIPSecAuditLogs", "enabled": "[parameters('logsEnabled')]"}, {"category": "AppServicePlatformLogs", "enabled": "[parameters('logsEnabled')]"}]}}, "resources": [{"type": "Microsoft.Web/sites/providers/diagnosticSettings", "apiVersion": "2017-05-01-preview", "name": "[concat(parameters('resourceName'), '/', 'Microsoft.Insights/', parameters('profileName'))]", "location": "[parameters('location')]", "dependsOn": [], "properties": {"workspaceId": "[parameters('logAnalytics')]", "metrics": [{"category": "AllMetrics", "enabled": "[parameters('metricsEnabled')]", "retentionPolicy": {"days": 0, "enabled": false}, "timeGrain": null}], "logs": "[if(startsWith(reference(parameters('serverFarmId'), '2021-03-01', 'Full').sku.tier, 'Premium'), variables('logs').premiumTierLogs, variables('logs').otherTierLogs)]"}}], "outputs": {"policy": {"type": "string", "value": "[concat(parameters('logAnalytics'), 'configured for diagnostic logs for ', ': ', parameters('resourceName'))]"}}}, "parameters": {"logAnalytics": {"value": "[parameters('logAnalytics')]"}, "location": {"value": "[field('location')]"}, "resourceName": {"value": "[field('name')]"}, "profileName": {"value": "[parameters('profileName')]"}, "metricsEnabled": {"value": "[parameters('metricsEnabled')]"}, "logsEnabled": {"value": "[parameters('logsEnabled')]"}, "serverFarmId": {"value": "[field('Microsoft.Web/sites/serverFarmId')]"}}}}}}}}}