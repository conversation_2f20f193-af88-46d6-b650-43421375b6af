{"name": "Deploy-SQL-Security", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "Deploy-SQL-Security.", "displayName": "Deploy-SQL-Security", "notScopes": [], "parameters": {}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/86a912f6-9a06-4e26-b447-11b16ba8659f", "nonComplianceMessages": [{"message": "SQL Server Security {enforcementMode} be enabled."}], "scope": "${current_scope_resource_id}", "enforcementMode": null}, "location": "${default_location}", "identity": {"type": "SystemAssigned"}}