{"name": "Enforce-Guardrails-KeyVault-Sup", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce additional recommended guardrails for Key Vault", "description": "This policy initiative is a group of policies that ensures Key Vault is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "<PERSON>", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"keyVaultManagedHsmDisablePublicNetworkModify": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "keyVaultModifyFw": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Modify-KV-PublicNetworkAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/84d327c3-164a-4685-b453-900478614456", "parameters": {"effect": {"value": "[parameters('keyVaultManagedHsmDisablePublicNetworkModify')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Modify-KV-Fw", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ac673a9a-f77d-4846-b2d8-a57f8e1c01dc", "parameters": {"effect": {"value": "[parameters('keyVaultModifyFw')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}