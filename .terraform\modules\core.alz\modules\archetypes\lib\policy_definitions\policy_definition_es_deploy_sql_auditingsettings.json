{"name": "Deploy-Sql-AuditingSettings", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Deploy SQL database auditing settings", "description": "Deploy auditing settings to SQL Database when it not exist in the deployment", "metadata": {"version": "1.0.0", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.Sql/servers/databases"}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Sql/servers/databases/auditingSettings", "name": "default", "existenceCondition": {"allOf": [{"field": "Microsoft.Sql/servers/databases/auditingSettings/state", "equals": "enabled"}, {"field": "Microsoft.Sql/servers/databases/auditingSettings/isAzureMonitorTargetEnabled", "equals": "true"}]}, "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "String"}, "sqlServerName": {"type": "String"}, "sqlServerDataBaseName": {"type": "String"}}, "variables": {}, "resources": [{"name": "[concat( parameters('sqlServerName'),'/',parameters('sqlServerDataBaseName'),'/default')]", "type": "Microsoft.Sql/servers/databases/auditingSettings", "apiVersion": "2017-03-01-preview", "properties": {"state": "enabled", "auditActionsAndGroups": ["BATCH_COMPLETED_GROUP", "DATABASE_OBJECT_CHANGE_GROUP", "SCHEMA_OBJECT_CHANGE_GROUP", "BACKUP_RESTORE_GROUP", "APPLICATION_ROLE_CHANGE_PASSWORD_GROUP", "DATABASE_PRINCIPAL_CHANGE_GROUP", "DATABASE_PRINCIPAL_IMPERSONATION_GROUP", "DATABASE_ROLE_MEMBER_CHANGE_GROUP", "USER_CHANGE_PASSWORD_GROUP", "DATABASE_OBJECT_OWNERSHIP_CHANGE_GROUP", "DATABASE_OBJECT_PERMISSION_CHANGE_GROUP", "DATABASE_PERMISSION_CHANGE_GROUP", "SCHEMA_OBJECT_PERMISSION_CHANGE_GROUP", "SUCCESSFUL_DATABASE_AUTHENTICATION_GROUP", "FAILED_DATABASE_AUTHENTICATION_GROUP"], "isAzureMonitorTargetEnabled": true}}], "outputs": {}}, "parameters": {"location": {"value": "[field('location')]"}, "sqlServerName": {"value": "[first(split(field('fullname'),'/'))]"}, "sqlServerDataBaseName": {"value": "[field('name')]"}}}}, "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/056cd41c-7e88-42e1-933e-88ba6a50c9c3"]}}}}}