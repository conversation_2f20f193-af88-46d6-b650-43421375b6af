{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deny-Public-Endpoints", "location": "${default_location}", "dependsOn": [], "properties": {"description": "This policy initiative is a group of policies that prevents creation of Azure PaaS services with exposed public endpoints", "displayName": "Public network access should be disabled for PaaS services", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Deny-PublicPaaSEndpoints", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Public network access {enforcementMode} be disabled for PaaS services."}], "parameters": {}, "scope": "${current_scope_resource_id}", "notScopes": []}, "identity": {"type": "None"}}