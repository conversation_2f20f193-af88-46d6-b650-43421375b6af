{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deploy-VM-ChangeTrack", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "Enable ChangeTracking and Inventory for virtual machines. Takes Data Collection Rule ID as parameter and asks for an option to input applicable locations and user-assigned identity for Azure Monitor Agent.", "displayName": "Enable ChangeTracking and Inventory for virtual machines", "policyDefinitionId": "/providers/Microsoft.Authorization/policySetDefinitions/92a36f05-ebc9-4bba-9128-b47ad2ea3354", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Change Tracking {enforcementMode} be enabled for Virtual Machines."}], "parameters": {"dcrResourceId": {"value": "${azure_monitor_data_collection_rule_change_tracking_resource_id}"}, "bringYourOwnUserAssignedManagedIdentity": {"value": true}, "restrictBringYourOwnUserAssignedIdentityToSubscription": {"value": false}, "userAssignedIdentityResourceId": {"value": "${user_assigned_managed_identity_resource_id}"}, "effect": {"value": "DeployIfNotExists"}}, "scope": "${current_scope_resource_id}", "notScopes": []}}