{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-ALZ-Decomm", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This initiative will help enforce and govern subscriptions that are placed within the decommissioned Management Group as part of your Subscription decommissioning process. See https://aka.ms/alz/policies for more information.", "displayName": "Enforce ALZ Decommissioned Guardrails", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-ALZ-Decomm", "enforcementMode": "<PERSON><PERSON><PERSON>", "parameters": {"listOfResourceTypesAllowed": {"value": ["microsoft.consumption/tags", "microsoft.authorization/roleassignments", "microsoft.authorization/roledefinitions", "microsoft.authorization/policyassignments", "microsoft.authorization/locks", "microsoft.authorization/policydefinitions", "microsoft.authorization/policysetdefinitions", "microsoft.resources/tags", "microsoft.authorization/roleeligibilityschedules", "microsoft.authorization/roleeligibilityscheduleinstances", "microsoft.authorization/roleassignmentschedules", "microsoft.authorization/roleassignmentscheduleinstances"]}}, "scope": "${current_scope_resource_id}", "notScopes": []}}