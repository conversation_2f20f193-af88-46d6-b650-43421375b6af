{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Deny-UnmanagedDisk", "dependsOn": [], "properties": {"description": "Deny virtual machines that do not use managed disk. It checks the managed disk property on virtual machine OS Disk fields.", "displayName": "Deny virtual machines and virtual machine scale sets that do not use managed disk", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Virtual machines and virtual machine scales sets {enforcementMode} use a managed disk."}], "overrides": [{"kind": "policyEffect", "value": "<PERSON><PERSON>"}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}, "location": "${default_location}", "identity": {"type": "None"}}