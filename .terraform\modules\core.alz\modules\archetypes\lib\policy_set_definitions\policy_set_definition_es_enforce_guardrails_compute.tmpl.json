{"name": "Enforce-Guardrails-Compute", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Compute", "description": "This policy initiative is a group of policies that ensures Compute is compliant per regulated Landing Zones.", "metadata": {"version": "1.0.0", "category": "Compute", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"diskDoubleEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "vmAndVmssEncryptionHost": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionReferenceId": "Deny-VmAndVmss-Encryption-Host", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fc4d8e41-e223-45ea-9bf5-eada37891d87", "parameters": {"effect": {"value": "[parameters('vmAndVmssEncryptionHost')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "Deny-<PERSON>sk-Double-Encryption", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ca91455f-eace-4f96-be59-e6e2c35b4816", "parameters": {"effect": {"value": "[parameters('diskDoubleEncryption')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}