{"name": "Deny-Resource-Locations", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "Specifies the allowed locations (regions) where Resources can be deployed.", "displayName": "Limit allowed locations for Resources", "notScopes": [], "parameters": {"listOfAllowedLocations": {"value": ["uksouth", "ukwest"]}}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e56962a6-4747-49cd-b67b-bf8b01975c4c", "nonComplianceMessages": [{"message": "Resources {enforcementMode} only be deployed to the allowed locations."}], "scope": "${current_scope_resource_id}", "enforcementMode": null}, "location": "${default_location}", "identity": {"type": "None"}}