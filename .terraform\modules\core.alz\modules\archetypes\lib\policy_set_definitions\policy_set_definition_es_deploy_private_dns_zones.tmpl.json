{"name": "Deploy-Private-DNS-Zones", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Configure Azure PaaS services to use private DNS zones", "description": "This policy initiative is a group of policies that ensures private endpoints to Azure PaaS services are integrated with Azure Private DNS zones", "metadata": {"version": "2.3.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"dnsZoneSubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "Subscription Id", "description": "The subscription id where the private DNS zones are deployed. If this is specified, it will override any individual private DNS zone resource ids specified."}}, "dnsZoneResourceGroupName": {"type": "string", "defaultValue": "", "metadata": {"displayName": "Resource Group Name", "description": "The resource group where the private DNS zones are deployed. If this is specified, it will override any individual private DNS zone resource ids specified."}}, "dnsZoneResourceType": {"type": "string", "defaultValue": "Microsoft.Network/privateDnsZones", "metadata": {"displayName": "Resource Type", "description": "The resource type where the private DNS zones are deployed. If this is specified, it will override any individual private DNS zone resource ids specified."}}, "dnsZoneRegion": {"type": "string", "defaultValue": "changeme", "metadata": {"displayName": "Region", "description": "The region where the private DNS zones are deployed. If this is specified, it will override any individual private DNS zone resource ids specified."}}, "dnzZoneRegionShortNames": {"type": "object", "defaultValue": {"changeme": "changeme", "australiacentral": "acl", "australiacentral2": "acl2", "australiaeast": "ae", "australiasoutheast": "ase", "brazilsoutheast": "bse", "brazilsouth": "brs", "canadacentral": "cnc", "canadaeast": "cne", "centralindia": "inc", "centralus": "cus", "centraluseuap": "ccy", "chilecentral": "clc", "eastasia": "ea", "eastus": "eus", "eastus2": "eus2", "eastus2euap": "ecy", "francecentral": "frc", "francesouth": "frs", "germanynorth": "gn", "germanywestcentral": "gwc", "israelcentral": "ilc", "italynorth": "itn", "japaneast": "jpe", "japanwest": "jpw", "koreacentral": "krc", "koreasouth": "krs", "malaysiasouth": "mys", "malaysiawest": "myw", "mexicocentral": "mxc", "newzealandnorth": "nzn", "northcentralus": "ncus", "northeurope": "ne", "norwayeast": "nwe", "norwaywest": "nww", "polandcentral": "plc", "qatarcentral": "qac", "southafricanorth": "san", "southafricawest": "saw", "southcentralus": "scus", "southeastasia": "sea", "southindia": "ins", "spaincentral": "spc", "swedencentral": "sdc", "swedensouth": "sds", "switzerlandnorth": "szn", "switzerlandwest": "szw", "taiwannorth": "twn", "uaecentral": "uac", "uaenorth": "uan", "uksouth": "uks", "ukwest": "ukw", "westcentralus": "wcus", "westeurope": "we", "westindia": "inw", "westus": "wus", "westus2": "wus2", "westus3": "wus3"}, "metadata": {"displayName": "Region Short Name Mapping", "description": "Mapping of region to private DNS zone resource id. If the region is not specified, the default private DNS zone resource id will be used."}}, "dnsZoneNames": {"type": "object", "defaultValue": {"azureAcrPrivateDnsZoneId": "privatelink.azurecr.io", "azureAcrDataPrivateDnsZoneId": "{regionName}.data.privatelink.azurecr.io", "azureAppPrivateDnsZoneId": "privatelink.azconfig.io", "azureAppServicesPrivateDnsZoneId": "privatelink.azurewebsites.net", "azureArcGuestconfigurationPrivateDnsZoneId": "privatelink.guestconfiguration.azure.com", "azureArcHybridResourceProviderPrivateDnsZoneId": "privatelink.his.arc.azure.com", "azureArcKubernetesConfigurationPrivateDnsZoneId": "privatelink.dp.kubernetesconfiguration.azure.com", "azureAsrPrivateDnsZoneId": "privatelink.siterecovery.windowsazure.com", "azureAutomationDSCHybridPrivateDnsZoneId": "privatelink.azure-automation.net", "azureAutomationWebhookPrivateDnsZoneId": "privatelink.azure-automation.net", "azureBatchPrivateDnsZoneId": "privatelink.batch.azure.com", "azureBotServicePrivateDnsZoneId": "privatelink.directline.botframework.com", "azureCognitiveSearchPrivateDnsZoneId": "privatelink.search.windows.net", "azureCognitiveServicesPrivateDnsZoneId": "privatelink.cognitiveservices.azure.com", "azureCosmosCassandraPrivateDnsZoneId": "privatelink.cassandra.cosmos.azure.com", "azureCosmosGremlinPrivateDnsZoneId": "privatelink.gremlin.cosmos.azure.com", "azureCosmosMongoPrivateDnsZoneId": "privatelink.mongo.cosmos.azure.com", "azureCosmosSQLPrivateDnsZoneId": "privatelink.documents.azure.com", "azureCosmosTablePrivateDnsZoneId": "privatelink.table.cosmos.azure.com", "azureDataExplorerPrivateDnsZoneId": "privatelink.{regionName}.kusto.windows.net", "azureDataFactoryPortalPrivateDnsZoneId": "privatelink.adf.azure.com", "azureDataFactoryPrivateDnsZoneId": "privatelink.datafactory.azure.net", "azureDatabricksPrivateDnsZoneId": "privatelink.azuredatabricks.net", "azureDiskAccessPrivateDnsZoneId": "privatelink.blob.core.windows.net", "azureEventGridDomainsPrivateDnsZoneId": "privatelink.eventgrid.azure.net", "azureEventGridTopicsPrivateDnsZoneId": "privatelink.eventgrid.azure.net", "azureEventHubNamespacePrivateDnsZoneId": "privatelink.servicebus.windows.net", "azureFilePrivateDnsZoneId": "privatelink.afs.azure.net", "azureHDInsightPrivateDnsZoneId": "privatelink.azurehdinsight.net", "azureIotCentralPrivateDnsZoneId": "privatelink.azureiotcentral.com", "azureIotDeviceupdatePrivateDnsZoneId": "privatelink.azure-devices.net", "azureIotHubsPrivateDnsZoneId": "privatelink.azure-devices.net", "azureIotPrivateDnsZoneId": "privatelink.azure-devices-provisioning.net", "azureKeyVaultPrivateDnsZoneId": "privatelink.vaultcore.azure.net", "azureKubernetesManagementPrivateDnsZoneId": "privatelink.{regionName}.azmk8s.io", "azureMachineLearningWorkspacePrivateDnsZoneId": "privatelink.api.azureml.ms", "azureMachineLearningWorkspaceSecondPrivateDnsZoneId": "privatelink.notebooks.azure.net", "azureManagedGrafanaWorkspacePrivateDnsZoneId": "privatelink.grafana.azure.com", "azureMediaServicesKeyPrivateDnsZoneId": "privatelink.media.azure.net", "azureMediaServicesLivePrivateDnsZoneId": "privatelink.media.azure.net", "azureMediaServicesStreamPrivateDnsZoneId": "privatelink.media.azure.net", "azureMigratePrivateDnsZoneId": "privatelink.prod.migration.windowsazure.com", "azureMonitorPrivateDnsZoneId1": "privatelink.monitor.azure.com", "azureMonitorPrivateDnsZoneId2": "privatelink.oms.opinsights.azure.com", "azureMonitorPrivateDnsZoneId3": "privatelink.ods.opinsights.azure.com", "azureMonitorPrivateDnsZoneId4": "privatelink.agentsvc.azure-automation.net", "azureMonitorPrivateDnsZoneId5": "privatelink.blob.core.windows.net", "azureRedisCachePrivateDnsZoneId": "privatelink.redis.cache.windows.net", "azureServiceBusNamespacePrivateDnsZoneId": "privatelink.servicebus.windows.net", "azureSignalRPrivateDnsZoneId": "privatelink.service.signalr.net", "azureSiteRecoveryBackupPrivateDnsZoneId": "privatelink.{regionCode}.backup.windowsazure.com", "azureSiteRecoveryBlobPrivateDnsZoneId": "privatelink.blob.core.windows.net", "azureSiteRecoveryQueuePrivateDnsZoneId": "privatelink.queue.core.windows.net", "azureStorageBlobPrivateDnsZoneId": "privatelink.blob.core.windows.net", "azureStorageBlobSecPrivateDnsZoneId": "privatelink.blob.core.windows.net", "azureStorageDFSPrivateDnsZoneId": "privatelink.dfs.core.windows.net", "azureStorageDFSSecPrivateDnsZoneId": "privatelink.dfs.core.windows.net", "azureStorageFilePrivateDnsZoneId": "privatelink.file.core.windows.net", "azureStorageQueuePrivateDnsZoneId": "privatelink.queue.core.windows.net", "azureStorageQueueSecPrivateDnsZoneId": "privatelink.queue.core.windows.net", "azureStorageStaticWebPrivateDnsZoneId": "privatelink.web.core.windows.net", "azureStorageStaticWebSecPrivateDnsZoneId": "privatelink.web.core.windows.net", "azureStorageTablePrivateDnsZoneId": "privatelink.table.core.windows.net", "azureStorageTableSecondaryPrivateDnsZoneId": "privatelink.table.core.windows.net", "azureSynapseDevPrivateDnsZoneId": "privatelink.dev.azuresynapse.net", "azureSynapseSQLPrivateDnsZoneId": "privatelink.sql.azuresynapse.net", "azureSynapseSQLODPrivateDnsZoneId": "privatelink.sql.azuresynapse.net", "azureVirtualDesktopHostpoolPrivateDnsZoneId": "privatelink.wvd.microsoft.com", "azureVirtualDesktopWorkspacePrivateDnsZoneId": "privatelink.wvd.microsoft.com", "azureWebPrivateDnsZoneId": "privatelink.webpubsub.azure.com"}, "metadata": {"displayName": "DNS Zone Names", "description": "The list of private DNS zone names to be used for the Azure PaaS services."}}, "azureFilePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureFilePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAutomationWebhookPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAutomationWebhookPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAutomationDSCHybridPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAutomationDSCHybridPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCosmosSQLPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCosmosSQLPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCosmosMongoPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCosmosMongoPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCosmosCassandraPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCosmosCassandraPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCosmosGremlinPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCosmosGremlinPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCosmosTablePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCosmosTablePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureDataFactoryPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureDataFactoryPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureDataFactoryPortalPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureDataFactoryPortalPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureDatabricksPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureDatabricksPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureHDInsightPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureHDInsightPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMigratePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMigratePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageBlobPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageBlobPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageBlobSecPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageBlobSecPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageQueuePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageQueuePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageQueueSecPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageQueueSecPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageFilePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageFilePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageStaticWebPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageStaticWebPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageStaticWebSecPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageStaticWebSecPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageDFSPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageDFSPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageDFSSecPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageDFSSecPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureSynapseSQLPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureSynapseSQLPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureSynapseSQLODPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureSynapseSQLODPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureSynapseDevPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureSynapseDevPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMediaServicesKeyPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMediaServicesKeyPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMediaServicesLivePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMediaServicesLivePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMediaServicesStreamPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMediaServicesStreamPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMonitorPrivateDnsZoneId1": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMonitorPrivateDnsZoneId1", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMonitorPrivateDnsZoneId2": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMonitorPrivateDnsZoneId2", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMonitorPrivateDnsZoneId3": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMonitorPrivateDnsZoneId3", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMonitorPrivateDnsZoneId4": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMonitorPrivateDnsZoneId4", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMonitorPrivateDnsZoneId5": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMonitorPrivateDnsZoneId5", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureWebPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureWebPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureBatchPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureBatchPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAppPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAppPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAsrPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAsrPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureIotPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureIotPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureKeyVaultPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureKeyVaultPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureSignalRPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureSignalRPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAppServicesPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAppServicesPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureEventGridTopicsPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureEventGridTopicsPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureDiskAccessPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureDiskAccessPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCognitiveServicesPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCognitiveServicesPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureIotHubsPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureIotHubsPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureEventGridDomainsPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureEventGridDomainsPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureRedisCachePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureRedisCachePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAcrPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAcrPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureEventHubNamespacePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureEventHubNamespacePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMachineLearningWorkspacePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMachineLearningWorkspacePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMachineLearningWorkspaceSecondPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMachineLearningWorkspaceSecondPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureServiceBusNamespacePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureServiceBusNamespacePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCognitiveSearchPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCognitiveSearchPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureBotServicePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureBotServicePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureManagedGrafanaWorkspacePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureManagedGrafanaWorkspacePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureVirtualDesktopHostpoolPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureVirtualDesktopHostpoolPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureVirtualDesktopWorkspacePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureVirtualDesktopWorkspacePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureIotDeviceupdatePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureIotDeviceupdatePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureArcGuestconfigurationPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureArcGuestconfigurationPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureArcHybridResourceProviderPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureArcHybridResourceProviderPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureArcKubernetesConfigurationPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureArcKubernetesConfigurationPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureIotCentralPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureIotCentralPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageTablePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageTablePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureStorageTableSecondaryPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureStorageTableSecondaryPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureSiteRecoveryBackupPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureSiteRecoveryBackupPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureSiteRecoveryBlobPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureSiteRecoveryBlobPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureSiteRecoveryQueuePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureSiteRecoveryQueuePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "effect": {"type": "string", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists"}, "effect1": {"type": "string", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["deployIfNotExists", "Disabled"], "defaultValue": "deployIfNotExists"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-File-Sync", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06695360-db88-47f6-b976-7500d4297475", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureFilePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureFilePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Automation-Webhook", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6dd01e4f-1be1-4e80-9d0b-d109e04cb064", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureAutomationWebhookPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureAutomationWebhookPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "Webhook"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Automation-DSCHybrid", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6dd01e4f-1be1-4e80-9d0b-d109e04cb064", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureAutomationDSCHybridPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureAutomationDSCHybridPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "DSCAndHybridWorker"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Cosmos-SQL", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a63cc0bd-cda4-4178-b705-37dc439d3e0f", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureCosmosSQLPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureCosmosSQLPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "SQL"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Cosmos-MongoDB", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a63cc0bd-cda4-4178-b705-37dc439d3e0f", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureCosmosMongoPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureCosmosMongoPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "MongoDB"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Cosmos-Cassandra", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a63cc0bd-cda4-4178-b705-37dc439d3e0f", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureCosmosCassandraPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureCosmosCassandraPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "<PERSON>"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Cosmos-Gremlin", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a63cc0bd-cda4-4178-b705-37dc439d3e0f", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureCosmosGremlinPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureCosmosGremlinPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "Gremlin"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Cosmos-Table", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a63cc0bd-cda4-4178-b705-37dc439d3e0f", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureCosmosTablePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureCosmosTablePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "Table"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-DataFactory", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/86cd96e1-1745-420d-94d4-d3f2fe415aa4", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureDataFactoryPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureDataFactoryPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "listOfGroupIds": {"value": ["dataFactory"]}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-DataFactory-Portal", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/86cd96e1-1745-420d-94d4-d3f2fe415aa4", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureDataFactoryPortalPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureDataFactoryPortalPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "listOfGroupIds": {"value": ["portal"]}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Databricks-UI-Api", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0eddd7f3-3d9b-4927-a07a-806e8ac9486c", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureDatabricksPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureDatabricksPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "groupId": {"value": "databricks_ui_api"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Databricks-Browser-AuthN", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0eddd7f3-3d9b-4927-a07a-806e8ac9486c", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureDatabricksPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureDatabricksPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "groupId": {"value": "browser_authentication"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-HDInsight", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/43d6e3bd-fc6a-4b44-8b4d-2151d8736a11", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureHDInsightPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureHDInsightPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "groupId": {"value": "cluster"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Migrate", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7590a335-57cf-4c95-babd-ecbc8fafeb1f", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMigratePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMigratePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-Blob", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/75973700-529f-4de2-b794-fb9b6781b6b0", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageBlobPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageBlobPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-Blob-Sec", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d847d34b-9337-4e2d-99a5-767e5ac9c582", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageBlobSecPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageBlobSecPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-Queue", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bcff79fb-2b0d-47c9-97e5-3023479b00d1", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageQueuePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageQueuePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-Queue-Sec", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/da9b4ae8-5ddc-48c5-b9c0-25f8abf7a3d6", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageQueueSecPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageQueueSecPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-File", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6df98d03-368a-4438-8730-a93c4d7693d6", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageFilePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageFilePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-StaticWeb", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9adab2a5-05ba-4fbd-831a-5bf958d04218", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageStaticWebPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageStaticWebPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-StaticWeb-Sec", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d19ae5f1-b303-4b82-9ca8-7682749faf0c", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageStaticWebSecPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageStaticWebSecPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-DFS", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/83c6fe0f-2316-444a-99a1-1ecd8a7872ca", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageDFSPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageDFSPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-DFS-Sec", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/90bd4cb3-9f59-45f7-a6ca-f69db2726671", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageDFSSecPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageDFSSecPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Synapse-SQL", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1e5ed725-f16c-478b-bd4b-7bfa2f7940b9", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureSynapseSQLPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureSynapseSQLPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "targetSubResource": {"value": "Sql"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Synapse-SQL-OnDemand", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1e5ed725-f16c-478b-bd4b-7bfa2f7940b9", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureSynapseSQLODPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureSynapseSQLODPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "targetSubResource": {"value": "SqlOnDemand"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Synapse-Dev", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1e5ed725-f16c-478b-bd4b-7bfa2f7940b9", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureSynapseDevPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureSynapseDevPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "targetSubResource": {"value": "<PERSON>"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-MediaServices-Key", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b4a7f6c1-585e-4177-ad5b-c2c93f4bb991", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMediaServicesKeyPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMediaServicesKeyPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "groupId": {"value": "keydelivery"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-MediaServices-Live", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b4a7f6c1-585e-4177-ad5b-c2c93f4bb991", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMediaServicesLivePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMediaServicesLivePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "groupId": {"value": "liveevent"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-MediaServices-Stream", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b4a7f6c1-585e-4177-ad5b-c2c93f4bb991", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMediaServicesStreamPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMediaServicesStreamPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "groupId": {"value": "streamingendpoint"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Monitor", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/437914ee-c176-4fff-8986-7e05eb971365", "parameters": {"privateDnsZoneId1": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMonitorPrivateDnsZoneId1'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMonitorPrivateDnsZoneId1, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateDnsZoneId2": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMonitorPrivateDnsZoneId2'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMonitorPrivateDnsZoneId2, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateDnsZoneId3": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMonitorPrivateDnsZoneId3'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMonitorPrivateDnsZoneId3, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateDnsZoneId4": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMonitorPrivateDnsZoneId4'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMonitorPrivateDnsZoneId4, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateDnsZoneId5": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMonitorPrivateDnsZoneId5'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMonitorPrivateDnsZoneId5, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Web", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0b026355-49cb-467b-8ac4-f777874e175a", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureWebPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureWebPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Batch", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4ec38ebc-381f-45ee-81a4-acbc4be878f8", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureBatchPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureBatchPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-App", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7a860e27-9ca2-4fc6-822d-c2d248c300df", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureAppPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureAppPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Site-Recovery", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/942bd215-1a66-44be-af65-6a1c0318dbe2", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureAsrPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureAsrPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-IoT", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/aaa64d2d-2fa3-45e5-b332-0b031b9b30e8", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureIotPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureIotPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-KeyVault", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ac673a9a-f77d-4846-b2d8-a57f8e1c01d4", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureKeyVaultPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureKeyVaultPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-SignalR", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b0e86710-7fb7-4a6c-a064-32e9b829509e", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureSignalRPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureSignalRPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-AppServices", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b318f84a-b872-429b-ac6d-a01b96814452", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureAppServicesPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureAppServicesPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-EventGridTopics", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/baf19753-7502-405f-8745-370519b20483", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureEventGridTopicsPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureEventGridTopicsPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect1')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-DiskAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bc05b96c-0b36-4ca9-82f0-5c53f96ce05a", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureDiskAccessPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureDiskAccessPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-CognitiveServices", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c4bc6f10-cb41-49eb-b000-d5ab82e2a091", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureCognitiveServicesPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureCognitiveServicesPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-IoTHubs", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c99ce9c1-ced7-4c3e-aca0-10e69ce0cb02", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureIotHubsPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureIotHubsPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect1')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-EventGridDomains", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d389df0a-e0d7-4607-833c-75a6fdac2c2d", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureEventGridDomainsPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureEventGridDomainsPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect1')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-RedisCache", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e016b22b-e0eb-436d-8fd7-160c4eaed6e2", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureRedisCachePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureRedisCachePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-ACR", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9585a95-5b8c-4d03-b193-dc7eb5ac4c32", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureAcrPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureAcrPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-EventHubNamespace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ed66d4f5-8220-45dc-ab4a-20d1749c74e6", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureEventHubNamespacePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureEventHubNamespacePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-MachineLearningWorkspace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ee40564d-486e-4f68-a5ca-7a621edae0fb", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMachineLearningWorkspacePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMachineLearningWorkspacePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "secondPrivateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureMachineLearningWorkspaceSecondPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureMachineLearningWorkspaceSecondPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-ServiceBusNamespace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f0fcf93c-c063-4071-9668-c47474bd3564", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureServiceBusNamespacePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureServiceBusNamespacePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-CognitiveSearch", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fbc14a67-53e4-4932-abcc-2049c6706009", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureCognitiveSearchPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureCognitiveSearchPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-BotService", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6a4e6f44-f2af-4082-9702-033c9e88b9f8", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureBotServicePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureBotServicePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-ManagedGrafanaWorkspace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4c8537f8-cd1b-49ec-b704-18e82a42fd58", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureManagedGrafanaWorkspacePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureManagedGrafanaWorkspacePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-VirtualDesktopHostpool", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9427df23-0f42-4e1e-bf99-a6133d841c4a", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureVirtualDesktopHostpoolPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureVirtualDesktopHostpoolPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "connection"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-VirtualDesktopWorkspace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/34804460-d88b-4922-a7ca-537165e060ed", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureVirtualDesktopWorkspacePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureVirtualDesktopWorkspacePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateEndpointGroupId": {"value": "feed"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-IoTDeviceupdate", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a222b93a-e6c2-4c01-817f-21e092455b2a", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureIotDeviceupdatePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureIotDeviceupdatePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Arc", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/55c4db33-97b0-437b-8469-c4f4498f5df9", "parameters": {"privateDnsZoneIDForGuestConfiguration": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureArcGuestconfigurationPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureArcGuestconfigurationPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateDnsZoneIDForHybridResourceProvider": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureArcHybridResourceProviderPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureArcHybridResourceProviderPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateDnsZoneIDForKubernetesConfiguration": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureArcKubernetesConfigurationPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureArcKubernetesConfigurationPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-IoTCentral", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d627d7c6-ded5-481a-8f2e-7e16b1e6faf6", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureIotCentralPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureIotCentralPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-Table", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/028bbd88-e9b5-461f-9424-a1b63a7bee1a", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageTablePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageTablePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Storage-Table-Secondary", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c1d634a5-f73d-4cdd-889f-2cc7006eb47f", "parameters": {"privateDnsZoneId": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureStorageTableSecondaryPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureStorageTableSecondaryPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Site-Recovery-Backup", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/af783da1-4ad1-42be-800d-d19c70038820", "parameters": {"privateDnsZone-Backup": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureSiteRecoveryBackupPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureSiteRecoveryBackupPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateDnsZone-Blob": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureSiteRecoveryBlobPrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureSiteRecoveryBlobPrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "privateDnsZone-Queue": {"value": "[if(equals(parameters('dnsZoneSubscriptionId'), ''), parameters('azureSiteRecoveryQueuePrivateDnsZoneId'), format('/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}', parameters('dnsZoneSubscriptionId'), toLower(parameters('dnsZoneResourceGroupName')), parameters('dnsZoneResourceType'), replace(replace(parameters('dnsZoneNames').azureSiteRecoveryQueuePrivateDnsZoneId, '{regionName}', parameters('dnsZoneRegion')), '{regionCode}', parameters('dnzZoneRegionShortNames')[parameters('dnsZoneRegion')])))]"}, "effect": {"value": "[parameters('effect')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}