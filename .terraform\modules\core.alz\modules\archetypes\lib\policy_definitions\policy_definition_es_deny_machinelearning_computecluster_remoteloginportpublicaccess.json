{"name": "Deny-MachineLearning-ComputeCluster-RemoteLoginPortPublicAccess", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Deny public access of Azure Machine Learning clusters via SSH", "description": "Deny public access of Azure Machine Learning clusters via SSH.", "metadata": {"version": "1.1.0", "category": "Machine Learning", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.MachineLearningServices/workspaces/computes"}, {"field": "Microsoft.MachineLearningServices/workspaces/computes/computeType", "equals": "AmlCompute"}, {"anyOf": [{"field": "Microsoft.MachineLearningServices/workspaces/computes/remoteLoginPortPublicAccess", "exists": false}, {"field": "Microsoft.MachineLearningServices/workspaces/computes/remoteLoginPortPublicAccess", "notEquals": "Disabled"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}