{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-GR-Key<PERSON>ault", "dependsOn": [], "properties": {"description": "This initiative assignment enables recommended ALZ guardrails for Azure Key Vault.", "displayName": "Enforce recommended guardrails for Azure Key Vault", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-KeyVault", "enforcementMode": "<PERSON><PERSON><PERSON>", "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}, "location": "${default_location}", "identity": {"type": "None"}}