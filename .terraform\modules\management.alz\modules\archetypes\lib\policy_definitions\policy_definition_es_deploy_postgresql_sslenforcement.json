{"name": "Deploy-PostgreSQL-sslEnforcement", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Azure Database for PostgreSQL server deploy a specific min TLS version requirement and enforce SSL ", "description": "Deploy a specific min TLS version requirement and enforce SSL on Azure Database for PostgreSQL server. Enables secure server to client by enforce  minimal Tls Version to secure the connection between your database server and your client applications helps protect against 'man in the middle' attacks by encrypting the data stream between the server and your application. This configuration enforces that SSL is always enabled for accessing your database server.", "metadata": {"version": "1.2.0", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect Azure Database for PostgreSQL server", "description": "Enable or disable the execution of the policy minimum TLS version Azure Database for PostgreSQL server"}}, "minimalTlsVersion": {"type": "String", "defaultValue": "TLS1_2", "allowedValues": ["TLS1_2", "TLS1_0", "TLS1_1", "TLSEnforcementDisabled"], "metadata": {"displayName": "Select version for PostgreSQL server", "description": "Select version minimum TLS version Azure Database for PostgreSQL server to enforce"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.DBforPostgreSQL/servers"}, {"anyOf": [{"field": "Microsoft.DBforPostgreSQL/servers/sslEnforcement", "notEquals": "Enabled"}, {"field": "Microsoft.DBforPostgreSQL/servers/minimalTlsVersion", "less": "[parameters('minimalTlsVersion')]"}]}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.DBforPostgreSQL/servers", "existenceCondition": {"allOf": [{"field": "Microsoft.DBforPostgreSQL/servers/sslEnforcement", "equals": "Enabled"}, {"field": "Microsoft.DBforPostgreSQL/servers/minimalTlsVersion", "equals": "[parameters('minimalTlsVersion')]"}]}, "name": "current", "roleDefinitionIds": ["/providers/microsoft.authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c"], "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"resourceName": {"type": "String"}, "minimalTlsVersion": {"type": "String"}, "location": {"type": "String"}}, "variables": {}, "resources": [{"type": "Microsoft.DBforPostgreSQL/servers", "apiVersion": "2017-12-01", "name": "[concat(parameters('resourceName'))]", "location": "[parameters('location')]", "properties": {"sslEnforcement": "[if(equals(parameters('minimalTlsVersion'), 'TLSEnforcementDisabled'),'Disabled', 'Enabled')]", "minimalTlsVersion": "[parameters('minimalTlsVersion')]"}}], "outputs": {}}, "parameters": {"resourceName": {"value": "[field('name')]"}, "minimalTlsVersion": {"value": "[parameters('minimalTlsVersion')]"}, "location": {"value": "[field('location')]"}}}}}}}}}