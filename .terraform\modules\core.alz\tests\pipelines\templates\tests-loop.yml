---
parameters:
  - name: module_path
    type: string
  - name: run_type
    type: string

steps:
  - task: Bash@3
    displayName: "[terraform init]"
    inputs:
      targetType: "inline"
      script: "make tf-init"
    env:
      ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
      TEST_MODULE_PATH: "${{ parameters.module_path }}"
    condition: and(succeeded(), in('${{ parameters.run_type }}', 'unit', 'e2e', 'destroy'))

  - task: Bash@3
    displayName: "[terraform plan]"
    inputs:
      targetType: "inline"
      script: "make tf-plan"
    env:
      ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
      TEST_MODULE_PATH: "${{ parameters.module_path }}"
    condition: and(succeeded(), in('${{ parameters.run_type }}', 'unit'))

  - task: Bash@3
    displayName: "[terraform apply]"
    inputs:
      targetType: "inline"
      script: "make tf-apply"
    env:
      ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
      TEST_MODULE_PATH: "${{ parameters.module_path }}"
    condition: and(succeeded(), eq('${{ parameters.run_type }}', 'e2e'))

  - task: Bash@3
    displayName: "[terraform destroy]"
    inputs:
      targetType: "inline"
      script: "make tf-destroy"
    env:
      ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
      TEST_MODULE_PATH: "${{ parameters.module_path }}"
    condition: and(succeeded(), eq('${{ parameters.run_type }}', 'destroy'))
