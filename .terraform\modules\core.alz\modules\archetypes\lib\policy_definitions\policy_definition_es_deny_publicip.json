{"name": "Deny-PublicIP", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "[Deprecated] Deny the creation of public IP", "description": "[Deprecated] This policy denies creation of Public IPs under the assigned scope. Superseded by https://www.azadvertizer.net/azpolicyadvertizer/6c112d4e-5bc7-47ae-a041-ea2d9dccd749.html using appropriate assignment parameters.", "metadata": {"deprecated": true, "supersededBy": "6c112d4e-5bc7-47ae-a041-ea2d9dccd749", "version": "1.0.0-deprecated", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.Network/publicIPAddresses"}, "then": {"effect": "[parameters('effect')]"}}}}