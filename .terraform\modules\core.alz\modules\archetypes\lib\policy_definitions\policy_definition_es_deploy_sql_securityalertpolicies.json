{"name": "Deploy-Sql-SecurityAlertPolicies", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Deploy SQL Database security Alert Policies configuration with email admin accounts", "description": "Deploy the security Alert Policies configuration with email admin accounts when it not exist in current configuration", "metadata": {"version": "1.1.1", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "emailAddresses": {"type": "Array", "defaultValue": ["<EMAIL>", "<EMAIL>"]}}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.Sql/servers/databases"}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Sql/servers/databases/securityAlertPolicies", "existenceCondition": {"allOf": [{"field": "Microsoft.Sql/servers/databases/securityAlertPolicies/state", "equals": "Enabled"}]}, "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "String"}, "sqlServerName": {"type": "String"}, "sqlServerDataBaseName": {"type": "String"}, "emailAddresses": {"type": "Array"}}, "variables": {}, "resources": [{"name": "[concat(parameters('sqlServerName'),'/',parameters('sqlServerDataBaseName'),'/default')]", "type": "Microsoft.Sql/servers/databases/securityAlertPolicies", "apiVersion": "2018-06-01-preview", "properties": {"state": "Enabled", "disabledAlerts": [""], "emailAddresses": "[parameters('emailAddresses')]", "emailAccountAdmins": true, "storageEndpoint": null, "storageAccountAccessKey": "", "retentionDays": 0}}], "outputs": {}}, "parameters": {"location": {"value": "[field('location')]"}, "sqlServerName": {"value": "[first(split(field('fullname'),'/'))]"}, "sqlServerDataBaseName": {"value": "[field('name')]"}, "emailAddresses": {"value": "[parameters('emailAddresses')]"}}}}, "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/056cd41c-7e88-42e1-933e-88ba6a50c9c3"]}}}}}