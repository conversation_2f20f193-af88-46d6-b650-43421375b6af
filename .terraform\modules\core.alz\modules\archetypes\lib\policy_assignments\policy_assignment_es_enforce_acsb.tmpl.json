{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "Enforce-ACSB", "location": "${default_location}", "dependsOn": [], "identity": {"type": "SystemAssigned"}, "properties": {"description": "This initiative assignment enables Azure Compute Security Baseline compliance auditing for Windows and Linux virtual machines.", "displayName": "Enforce Azure Compute Security Baseline compliance auditing", "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policySetDefinitions/Enforce-ACSB", "enforcementMode": "<PERSON><PERSON><PERSON>", "nonComplianceMessages": [{"message": "Azure Compute Security Baseline compliance auditing {enforcementMode} be enforced."}], "scope": "${current_scope_resource_id}", "notScopes": [], "parameters": {}}}