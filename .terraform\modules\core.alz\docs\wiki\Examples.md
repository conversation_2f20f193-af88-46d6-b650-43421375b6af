<!-- markdownlint-disable first-line-h1 -->
The following examples are designed to help build an understanding of how to use the module, ranging from basic deployments covering the core resource hierarchy from Enterprise-scale, through to more advanced scenarios.

Use the links below to explore these examples in more detail.

### Basic (Level 100)

- [Deploy default configuration][wiki_deploy_default_configuration]
- [Deploy demo landing zone archetypes][wiki_deploy_demo_landing_zone_archetypes]

### Intermediate (Level 200)

- [Deploy custom landing zone archetypes][wiki_deploy_custom_landing_zone_archetypes]
- [Deploy connectivity resources (<PERSON><PERSON> and <PERSON><PERSON><PERSON>)][wiki_deploy_connectivity_resources]
- [Deploy connectivity resources (Virtual WAN)][wiki_deploy_virtual_wan_resources]
- [Deploy identity resources][wiki_deploy_identity_resources]
- [Deploy management resources][wiki_deploy_management_resources]
- [Assign a built-in policy][wiki_assign_a_built_in_policy]
- [Create and assign custom RBAC roles][wiki_create_and_assign_custom_rbac_roles]

### Advanced (Level 300)

- [Deploy connectivity resources with custom settings (<PERSON><PERSON> and <PERSON><PERSON><PERSON>)][wiki_deploy_connectivity_resources_custom]
- [Deploy connectivity resources with custom settings (Virtual WAN)][wiki_deploy_virtual_wan_resources_custom]
- [Deploy identity resources with custom settings][wiki_deploy_identity_resources_custom]
- [Deploy management resources with custom settings][wiki_deploy_management_resources_custom]
- [Expand built-in archetype definitions][wiki_expand_built_in_archetype_definitions]
- [Create custom policies, policy sets and assignments][wiki_create_custom_policies_policy_sets_and_assignments]
- [Override module role assignments][wiki_override_module_role_assignments]
- [Deploy policies without enforcing them]([Examples]-Deploy-policies-without-enforcing-them)

### Expert (Level 400)

- [Deploy using module nesting][wiki_deploy_using_module_nesting]
- [Deploy using multiple module declarations with orchestration][wiki_deploy_using_multiple_module_declarations_with_orchestration]
- [Deploy using multiple module declarations with remote state][wiki_deploy_using_multiple_module_declarations_with_remote_state]

[//]: # "************************"
[//]: # "INSERT LINK LABELS BELOW"
[//]: # "************************"

[wiki_assign_a_built_in_policy]:                                     %5BExamples%5D-Assign-a-Built-in-Policy "Wiki - Assign a built-in policy"
[wiki_create_and_assign_custom_rbac_roles]:                          %5BExamples%5D-Create-and-Assign-Custom-RBAC-Roles "Wiki - Create and assign custom RBAC roles"
[wiki_create_custom_policies_policy_sets_and_assignments]:           %5BExamples%5D-Create-Custom-Policies-Policy-Sets-and-Assignments "Wiki - Create custom policies, policy sets and assignments"
[wiki_deploy_connectivity_resources_custom]:                         %5BExamples%5D-Deploy-Multi-Region-Networking-With-Custom-Settings "Wiki - Deploy multi region networking with custom settings (Hub and Spoke)"
[wiki_deploy_connectivity_resources]:                                %5BExamples%5D-Deploy-Connectivity-Resources "Wiki - Deploy connectivity resources (Hub and Spoke)"
[wiki_deploy_custom_landing_zone_archetypes]:                        %5BExamples%5D-Deploy-Custom-Landing-Zone-Archetypes "Wiki - Deploy custom landing zone archetypes"
[wiki_deploy_default_configuration]:                                 %5BExamples%5D-Deploy-Default-Configuration "Wiki - Deploy default configuration"
[wiki_deploy_demo_landing_zone_archetypes]:                          %5BExamples%5D-Deploy-Demo-Landing-Zone-Archetypes "Wiki - Deploy demo landing zone archetypes"
[wiki_deploy_identity_resources_custom]:                             %5BExamples%5D-Deploy-Identity-Resources-With-Custom-Settings "Wiki - Deploy identity resources with custom settings"
[wiki_deploy_identity_resources]:                                    %5BExamples%5D-Deploy-Identity-Resources "Wiki - Deploy identity resources"
[wiki_deploy_management_resources_custom]:                           %5BExamples%5D-Deploy-Management-Resources-With-Custom-Settings "Wiki - Deploy management resources with custom settings"
[wiki_deploy_management_resources]:                                  %5BExamples%5D-Deploy-Management-Resources "Wiki - Deploy management resources"
[wiki_deploy_using_module_nesting]:                                  %5BExamples%5D-Deploy-Using-Module-Nesting "Wiki - Deploy using module nesting"
[wiki_deploy_using_multiple_module_declarations_with_orchestration]: %5BExamples%5D-Deploy-using-multiple-module-declarations-with-orchestration "Wiki - Deploy using multiple module declarations with orchestration"
[wiki_deploy_using_multiple_module_declarations_with_remote_state]:  %5BExamples%5D-Deploy-using-multiple-module-declarations-with-remote-state "Wiki - Deploy using multiple module declarations with remote state"
[wiki_deploy_virtual_wan_resources_custom]:                          %5BExamples%5D-Deploy-Virtual-WAN-Multi-Region-With-Custom-Settings "Wiki - Deploy multi region networking with custom settings (Virtual WAN)"
[wiki_deploy_virtual_wan_resources]:                                 %5BExamples%5D-Deploy-Virtual-WAN-Resources "Wiki - Deploy connectivity resources (Virtual WAN)"
[wiki_expand_built_in_archetype_definitions]:                        %5BExamples%5D-Expand-Built-in-Archetype-Definitions "Wiki - Expand built-in archetype definitions"
[wiki_override_module_role_assignments]:                             %5BExamples%5D-Override-Module-Role-Assignments "Wiki - Override module role assignments"
