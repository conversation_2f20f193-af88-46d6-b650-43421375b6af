---
name: "Tests (E2E)"

trigger: none

pool:
  vmImage: ubuntu-20.04

variables:
  - group: csu-tf-environment
  - name: PIPELINE_RUN_TYPE
    value: e2e

jobs:
  - job: matrix_generator
    displayName: "Matrix Generator"
    steps:
      - template: templates/tests-strategy.yml

  - job: backend_generator
    displayName: "Backend Storage Generator"
    steps:
      - template: templates/tests-backend.yml

  - job: run_e2e_tests_001
    displayName: "E2E Tests 001"
    dependsOn:
      - matrix_generator
      - backend_generator
    strategy:
      matrix: $[ dependencies.matrix_generator.outputs['build_strategy.matrix_json'] ]
    variables:
      STORAGE_ACCOUNT_RSG_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_RSG_NAME'] ]
      STORAGE_ACCOUNT_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_NAME'] ]
      STORAGE_CONTAINER_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_CONTAINER_NAME'] ]
    timeoutInMinutes: 120
    cancelTimeoutInMinutes: 120
    steps:
      - template: templates/tests-common.yml

      - template: templates/tests-loop.yml
        parameters:
          module_path: "tests/modules/test_001_baseline"
          run_type: e2e

  - job: run_e2e_tests_002
    displayName: "E2E Tests 002"
    dependsOn:
      - matrix_generator
      - backend_generator
      - run_e2e_tests_001
    strategy:
      matrix: $[ dependencies.matrix_generator.outputs['build_strategy.matrix_json'] ]
    variables:
      STORAGE_ACCOUNT_RSG_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_RSG_NAME'] ]
      STORAGE_ACCOUNT_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_NAME'] ]
      STORAGE_CONTAINER_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_CONTAINER_NAME'] ]
    timeoutInMinutes: 120
    cancelTimeoutInMinutes: 120
    steps:
      - template: templates/tests-common.yml

      - template: templates/tests-loop.yml
        parameters:
          module_path: "tests/modules/test_002_add_custom_core"
          run_type: e2e

  - job: run_e2e_tests_003
    displayName: "E2E Tests 003"
    dependsOn:
      - matrix_generator
      - backend_generator
      - run_e2e_tests_002
    strategy:
      matrix: $[ dependencies.matrix_generator.outputs['build_strategy.matrix_json'] ]
    variables:
      STORAGE_ACCOUNT_RSG_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_RSG_NAME'] ]
      STORAGE_ACCOUNT_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_NAME'] ]
      STORAGE_CONTAINER_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_CONTAINER_NAME'] ]
    timeoutInMinutes: 120
    cancelTimeoutInMinutes: 120
    steps:
      - template: templates/tests-common.yml

      - template: templates/tests-loop.yml
        parameters:
          module_path: "tests/modules/test_003_add_mgmt_conn"
          run_type: e2e

  - job: run_e2e_clean_up
    displayName: "E2E Clean-up"
    dependsOn:
      - matrix_generator
      - backend_generator
      - run_e2e_tests_003
    strategy:
      matrix: $[ dependencies.matrix_generator.outputs['build_strategy.matrix_json'] ]
    variables:
      STORAGE_ACCOUNT_RSG_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_RSG_NAME'] ]
      STORAGE_ACCOUNT_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_ACCOUNT_NAME'] ]
      STORAGE_CONTAINER_NAME: $[ dependencies.backend_generator.outputs['prepare_backend.STORAGE_CONTAINER_NAME'] ]
    timeoutInMinutes: 120
    cancelTimeoutInMinutes: 120
    condition: |
      or
      (
        and
        (
          or(failed(), canceled()),
          ne(variables.ALWAYS_DESTROY, 'false')
        ),
        succeeded()
      )
    steps:
      - template: templates/tests-common.yml

      - template: templates/tests-loop.yml
        parameters:
          module_path: "tests/modules/test_001_baseline"
          run_type: destroy
