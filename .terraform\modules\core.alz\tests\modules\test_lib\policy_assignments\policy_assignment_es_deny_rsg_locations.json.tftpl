{
  "name": "Deny-RSG-Locations",
  "type": "Microsoft.Authorization/policyAssignments",
  "apiVersion": "2019-09-01",
  "properties": {
    "description": "Specifies the allowed locations (regions) where Resource Groups can be deployed. Generated from custom Terraform template.",
    "displayName": "Limit allowed locations for Resource Groups",
    "notScopes": [],
    "parameters": {
      "listOfAllowedLocations": ${jsonencode({
        "value": [for location in listOfAllowedLocations : "${location}"]
      })}
    },
    "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e765b5de-1225-4ba3-bd56-1ac6695af988",
    "nonComplianceMessages": [
      {
        "message": "Resource Groups {enforcementMode} be deployed in the allowed locations."
      }
    ],
    "scope": "${current_scope_resource_id}",
    "enforcementMode": null
  },
  "location": "${default_location}",
  "identity": {
    "type": "None"
  }
}