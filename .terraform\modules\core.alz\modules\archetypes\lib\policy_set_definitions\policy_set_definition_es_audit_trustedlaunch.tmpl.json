{"name": "Audit-TrustedLaunch", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Audit virtual machines for Trusted Launch support", "description": "Trusted Launch improves security of a Virtual Machine which requires VM SKU, OS Disk & OS Image to support it (Gen 2). To learn more about Trusted Launch, visit https://aka.ms/trustedlaunch.", "metadata": {"version": "1.0.0", "category": "Trusted Launch", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "AuditDisksOsTrustedLaunch", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b03bb370-5249-4ea4-9fce-2552e87e45fa", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AuditTrustedLaunchEnabled", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}