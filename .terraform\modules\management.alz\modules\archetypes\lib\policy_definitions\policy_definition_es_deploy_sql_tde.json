{"name": "Deploy-Sql-Tde", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "[Deprecated] Deploy SQL Database Transparent Data Encryption", "description": "Deploy the Transparent Data Encryption when it is not enabled in the deployment. Please use this policy instead https://www.azadvertizer.net/azpolicyadvertizer/86a912f6-9a06-4e26-b447-11b16ba8659f.html", "metadata": {"deprecated": true, "supersededBy": "86a912f6-9a06-4e26-b447-11b16ba8659f", "version": "1.1.1-deprecated", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "excludedDatabases": {"type": "Array", "metadata": {"displayName": "Excluded Databases", "description": "Array of databases that are excluded from this policy"}, "defaultValue": ["master", "model", "tempdb", "msdb", "resource"]}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Sql/servers/databases"}, {"field": "name", "notIn": "[parameters('excludedDatabases')]"}]}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Sql/servers/databases/transparentDataEncryption", "existenceCondition": {"allOf": [{"field": "Microsoft.Sql/transparentDataEncryption.status", "equals": "Enabled"}]}, "deployment": {"properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "String"}, "sqlServerName": {"type": "String"}, "sqlServerDataBaseName": {"type": "String"}}, "variables": {}, "resources": [{"name": "[concat( parameters('sqlServerName'),'/',parameters('sqlServerDataBaseName'),'/current')]", "type": "Microsoft.Sql/servers/databases/transparentDataEncryption", "apiVersion": "2014-04-01", "properties": {"status": "Enabled"}}], "outputs": {}}, "parameters": {"location": {"value": "[field('location')]"}, "sqlServerName": {"value": "[first(split(field('fullname'),'/'))]"}, "sqlServerDataBaseName": {"value": "[field('name')]"}}}}, "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/056cd41c-7e88-42e1-933e-88ba6a50c9c3"]}}}}}